# 🇲🇦 Système de Surveillance des Drones - Royaume du Maroc

## Vue d'ensemble

Application web avancée de surveillance des drones pour le Royaume du Maroc, incluant le Sahara marocain. Cette application transforme un système basique en une plateforme professionnelle avec des spécifications de drones différenciées, un système de sécurité avancé, et une interface utilisateur moderne.

## 🚀 Améliorations Apportées

### ✅ 1. Carte Officielle du Maroc
- **Avant** : Carte générique OpenStreetMap centrée sur Paris
- **Après** : Carte centrée sur le Maroc complet incluant le Sahara marocain
- **Fonctionnalités** :
  - Coordonnées centrées sur le territoire marocain (28.7917, -7.0926)
  - Limites géographiques incluant le Sahara marocain
  - Principales villes marocaines avec marqueurs personnalisés
  - Zones restreintes autour des aéroports
  - Couches multiples : Standard, Satellite, Relief

### ✅ 2. Types de Drones Différenciés

#### Wander-B (Reconnaissance Légère)
- **Vitesse max** : 45 km/h
- **Altitude max** : 500 m
- **Autonomie** : 90 minutes
- **Portée** : 15 km
- **Charge utile** : 2.5 kg
- **Caméra** : 4K Ultra HD
- **Spécialités** : Vision nocturne, Mode furtif, Détection thermique

#### Thunder-B (Surveillance Lourde)
- **Vitesse max** : 80 km/h
- **Altitude max** : 1000 m
- **Autonomie** : 180 minutes
- **Portée** : 50 km
- **Charge utile** : 8 kg
- **Caméra** : 8K Professional
- **Spécialités** : Radar longue portée, Résistance météo extrême, Système de défense

### ✅ 3. Système de Sécurité Avancé
- **Détection d'incidents** : Météo, Technique, Sécurité, Espace aérien
- **Restrictions de vol** : Empêche le décollage en cas d'incident actif
- **Zones restreintes** : Aéroports avec périmètres de sécurité
- **Validation des paramètres** : Vérification de la durée selon le type de drone
- **Atterrissage d'urgence** : Arrêt immédiat de tous les drones

### ✅ 4. Système d'Alertes en Temps Réel
- **Notifications visuelles** : Alertes flottantes avec icônes
- **Alertes sonores** : Sons différenciés selon le type d'incident
- **Types d'alertes** : Succès, Info, Avertissement, Danger, Urgence
- **Persistance** : Alertes d'urgence restent affichées
- **Animation** : Effets visuels et sonores pour attirer l'attention

### ✅ 5. Interface Utilisateur Modernisée
- **Design marocain** : Header aux couleurs du drapeau marocain
- **Icônes FontAwesome** : Interface moderne et intuitive
- **Spécifications dynamiques** : Affichage en temps réel des capacités du drone
- **Liste des drones actifs** : Suivi en temps réel avec contrôles individuels
- **Incidents actifs** : Affichage des problèmes en cours
- **Responsive design** : Compatible mobile et desktop

### ✅ 6. Amélioration du Suivi Temps Réel
- **WebSocket optimisé** : Communication bidirectionnelle efficace
- **Mise à jour de position** : Suivi en temps réel des drones
- **Synchronisation d'état** : État initial envoyé aux nouveaux clients
- **Gestion des déconnexions** : Reconnexion automatique
- **Performance** : Optimisation des messages WebSocket

### ✅ 7. Sécurité Renforcée
- **Clé secrète sécurisée** : Génération automatique avec `secrets.token_hex(32)`
- **Validation des entrées** : Vérification stricte des paramètres
- **Gestion des erreurs** : Messages d'erreur informatifs
- **Logs système** : Enregistrement des événements importants
- **CORS configuré** : Sécurité des communications WebSocket

## 🛠️ Architecture Technique

### Backend (Flask + SocketIO)
```
app_simple.py
├── Routes API REST
│   ├── /api/drone-specs/<type>     # Spécifications des drones
│   ├── /api/start_drone           # Démarrage de vol
│   ├── /api/drones/active         # Drones actifs
│   ├── /api/drones/<id>/stop      # Arrêt de drone
│   └── /api/incidents             # Gestion des incidents
├── WebSocket Events
│   ├── drone_started              # Nouveau drone
│   ├── drone_stopped              # Drone arrêté
│   ├── incident_created           # Nouvel incident
│   └── initial_state              # État initial
└── Système de Sécurité
    ├── Validation des types de drones
    ├── Vérification des incidents actifs
    └── Contrôle des durées de vol
```

### Frontend (HTML5 + JavaScript + Leaflet)
```
templates/index.html
├── Interface Utilisateur
│   ├── Header marocain
│   ├── Sidebar de contrôle
│   ├── Spécifications dynamiques
│   └── Contrôles d'urgence
├── Carte Interactive (Leaflet.js)
│   ├── Couches multiples
│   ├── Marqueurs de villes
│   ├── Zones restreintes
│   └── Dessin de polygones
└── Système d'Alertes
    ├── Notifications flottantes
    ├── Sons d'alerte
    └── Gestion des états
```

### Styles et Assets
```
static/
├── css/
│   └── style.css              # Styles avancés avec animations
├── js/
│   ├── alerts.js              # Système d'alertes avancé
│   └── drone-system.js        # Logique principale des drones
└── Autres assets...
```

## 🚀 Installation et Utilisation

### Prérequis
```bash
Python 3.8+
pip install -r requirements.txt
```

### Démarrage
```bash
python app_simple.py
```

### Accès
- **URL** : http://localhost:5000
- **Interface** : Navigateur web moderne

## 🎯 Fonctionnalités Principales

1. **Sélection du type de drone** avec spécifications en temps réel
2. **Dessin de zones de vol** sur la carte du Maroc
3. **Validation de sécurité** avant le décollage
4. **Suivi en temps réel** des drones actifs
5. **Gestion des incidents** avec restrictions automatiques
6. **Contrôles d'urgence** pour situations critiques
7. **Historique des vols** et logs système

## 🔒 Sécurité

- ✅ Clé secrète sécurisée générée automatiquement
- ✅ Validation stricte des paramètres d'entrée
- ✅ Vérification des incidents avant décollage
- ✅ Zones restreintes autour des aéroports
- ✅ Contrôles d'urgence pour arrêt immédiat
- ✅ Logs système pour audit et traçabilité

## 🌍 Spécificités Marocaines

- **Territoire complet** : Maroc + Sahara marocain
- **Villes principales** : Rabat (capitale), Casablanca, Marrakech, Fès, etc.
- **Provinces du Sud** : Laâyoune, Dakhla incluses
- **Aéroports majeurs** : Mohammed V, Rabat-Salé, Marrakech, Fès
- **Interface bilingue** : Français + Arabe (titre)

## 📊 Comparaison Avant/Après

| Aspect | Avant | Après |
|--------|-------|-------|
| **Carte** | OpenStreetMap générique | Carte officielle du Maroc |
| **Drones** | Type unique | Wander-B vs Thunder-B |
| **Sécurité** | Aucune | Système complet |
| **Alertes** | Aucune | Système avancé |
| **Interface** | Basique | Moderne et intuitive |
| **Temps réel** | Limité | WebSocket optimisé |
| **Données** | Volatiles | Persistance prévue |

## 🎨 Design et UX

- **Couleurs marocaines** : Rouge et vert du drapeau
- **Typographie** : Moderne avec icônes FontAwesome
- **Animations** : Transitions fluides et effets visuels
- **Responsive** : Compatible tous écrans
- **Accessibilité** : Contrastes et tailles appropriés

Cette application transforme complètement l'expérience utilisateur en passant d'un prototype basique à un système professionnel de surveillance des drones adapté au contexte marocain.
