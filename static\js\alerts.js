// Système d'alertes avancé pour l'application de surveillance de drones

class AlertSystem {
    constructor() {
        this.notifications = [];
        this.maxNotifications = 5;
        this.audioContext = null;
        this.alertSounds = {};
        this.init();
    }

    init() {
        this.createNotificationContainer();
        this.initAudioSystem();
    }

    createNotificationContainer() {
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 350px;
            `;
            document.body.appendChild(container);
        }
    }

    async initAudioSystem() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.createAlertSounds();
        } catch (error) {
            console.warn('Audio non supporté:', error);
        }
    }

    createAlertSounds() {
        if (!this.audioContext) return;

        // Son d'alerte standard
        this.alertSounds.standard = () => {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, this.audioContext.currentTime + 0.1);
            oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime + 0.2);
            
            gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + 0.3);
        };

        // Son d'urgence
        this.alertSounds.emergency = () => {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            oscillator.frequency.setValueAtTime(1000, this.audioContext.currentTime);
            oscillator.frequency.setValueAtTime(500, this.audioContext.currentTime + 0.1);
            oscillator.frequency.setValueAtTime(1000, this.audioContext.currentTime + 0.2);
            oscillator.frequency.setValueAtTime(500, this.audioContext.currentTime + 0.3);
            
            gainNode.gain.setValueAtTime(0.5, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.4);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + 0.4);
        };

        // Son de succès
        this.alertSounds.success = () => {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            oscillator.frequency.setValueAtTime(523, this.audioContext.currentTime); // Do
            oscillator.frequency.setValueAtTime(659, this.audioContext.currentTime + 0.1); // Mi
            oscillator.frequency.setValueAtTime(784, this.audioContext.currentTime + 0.2); // Sol
            
            gainNode.gain.setValueAtTime(0.2, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + 0.3);
        };
    }

    playSound(type = 'standard') {
        if (this.alertSounds[type] && this.audioContext && this.audioContext.state === 'running') {
            this.alertSounds[type]();
        }
    }

    show(title, message, type = 'info', options = {}) {
        const notification = this.createNotification(title, message, type, options);
        this.addNotification(notification);
        
        // Jouer le son approprié
        if (options.playSound !== false) {
            switch (type) {
                case 'danger':
                case 'emergency':
                    this.playSound('emergency');
                    break;
                case 'success':
                    this.playSound('success');
                    break;
                case 'warning':
                    this.playSound('standard');
                    break;
            }
        }

        return notification;
    }

    createNotification(title, message, type, options) {
        const notification = document.createElement('div');
        const id = 'notification-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        
        notification.id = id;
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.style.cssText = `
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: none;
            border-radius: 8px;
            animation: slideInRight 0.3s ease-out;
        `;
        
        const icon = this.getIcon(type);
        const duration = options.duration || this.getDuration(type);
        
        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="${icon} me-2"></i>
                <div class="flex-grow-1">
                    <strong>${title}</strong><br>
                    <small>${message}</small>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            ${options.showProgress ? `<div class="progress mt-2" style="height: 3px;">
                <div class="progress-bar" role="progressbar" style="width: 100%; transition: width ${duration}ms linear;"></div>
            </div>` : ''}
        `;

        // Auto-dismiss
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(id);
            }, duration);

            // Progress bar animation
            if (options.showProgress) {
                setTimeout(() => {
                    const progressBar = notification.querySelector('.progress-bar');
                    if (progressBar) {
                        progressBar.style.width = '0%';
                    }
                }, 100);
            }
        }

        return notification;
    }

    getIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            info: 'fas fa-info-circle',
            warning: 'fas fa-exclamation-triangle',
            danger: 'fas fa-exclamation-circle',
            emergency: 'fas fa-skull-crossbones'
        };
        return icons[type] || icons.info;
    }

    getDuration(type) {
        const durations = {
            success: 3000,
            info: 4000,
            warning: 6000,
            danger: 8000,
            emergency: 0 // Ne se ferme pas automatiquement
        };
        return durations[type] || 4000;
    }

    addNotification(notification) {
        const container = document.getElementById('notification-container');
        
        // Limiter le nombre de notifications
        while (this.notifications.length >= this.maxNotifications) {
            const oldest = this.notifications.shift();
            if (oldest && oldest.parentNode) {
                oldest.remove();
            }
        }

        container.appendChild(notification);
        this.notifications.push(notification);

        // Animation d'entrée
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
    }

    removeNotification(id) {
        const notification = document.getElementById(id);
        if (notification) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
                this.notifications = this.notifications.filter(n => n.id !== id);
            }, 300);
        }
    }

    clear() {
        this.notifications.forEach(notification => {
            if (notification.parentNode) {
                notification.remove();
            }
        });
        this.notifications = [];
    }

    // Méthodes de convenance
    success(title, message, options = {}) {
        return this.show(title, message, 'success', options);
    }

    info(title, message, options = {}) {
        return this.show(title, message, 'info', options);
    }

    warning(title, message, options = {}) {
        return this.show(title, message, 'warning', options);
    }

    danger(title, message, options = {}) {
        return this.show(title, message, 'danger', options);
    }

    emergency(title, message, options = {}) {
        return this.show(title, message, 'emergency', { ...options, duration: 0 });
    }
}

// CSS pour les animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);

// Instance globale du système d'alertes
window.alertSystem = new AlertSystem();

// Fonction de compatibilité avec l'ancien code
window.showNotification = function(title, message, type = 'info', options = {}) {
    return window.alertSystem.show(title, message, type, options);
};

window.playAlertSound = function(type = 'standard') {
    return window.alertSystem.playSound(type);
};
