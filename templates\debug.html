<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - <PERSON><PERSON></title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        #map {
            height: 500px;
            width: 100%;
            border: 2px solid #333;
            background-color: #f0f0f0;
        }
        
        .morocco-header {
            background: linear-gradient(135deg, #c1272d 0%, #006233 50%, #c1272d 100%);
            color: white;
            padding: 10px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .city-icon {
            background-color: rgba(52, 152, 219, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            border: 2px solid white;
            white-space: nowrap;
        }
        
        .city-icon.capital {
            background-color: rgba(231, 76, 60, 0.9);
            border-color: #f1c40f;
        }
        
        .city-marker {
            background: transparent !important;
            border: none !important;
        }
    </style>
</head>
<body>
    <!-- Header avec identité marocaine -->
    <div class="morocco-header">
        <div class="container-fluid">
            <h4 class="mb-0">
                <span>🇲🇦</span>
                Debug - Système de Surveillance des Drones - Royaume du Maroc
            </h4>
        </div>
    </div>

    <div class="container-fluid mt-3">
        <div class="row">
            <!-- Sidebar de contrôle -->
            <div class="col-md-3">
                <h5 class="text-center mb-4">
                    <i class="fas fa-bug me-2"></i>
                    Debug Console
                </h5>
                
                <div class="mb-3">
                    <h6>État du système:</h6>
                    <div id="systemStatus" class="alert alert-info">
                        Initialisation...
                    </div>
                </div>
                
                <div class="mb-3">
                    <h6>Logs:</h6>
                    <div id="logs" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;">
                    </div>
                </div>
                
                <button class="btn btn-primary w-100" onclick="testMap()">
                    <i class="fas fa-play me-1"></i>
                    Tester la carte
                </button>
            </div>

            <!-- Carte principale -->
            <div class="col-md-9">
                <div id="map"></div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let map;
        const logs = document.getElementById('logs');
        const systemStatus = document.getElementById('systemStatus');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            logs.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message, type = 'info') {
            systemStatus.className = `alert alert-${type}`;
            systemStatus.textContent = message;
        }
        
        // Configuration du Maroc
        const MOROCCO_CONFIG = {
            center: [28.7917, -7.0926],
            cities: [
                {name: "Rabat", coords: [34.0209, -6.8416], capital: true, region: "Rabat-Salé-Kénitra"},
                {name: "Casablanca", coords: [33.5731, -7.5898], region: "Casablanca-Settat"},
                {name: "Marrakech", coords: [31.6295, -7.9811], region: "Marrakech-Safi"},
                {name: "Fès", coords: [34.0181, -5.0078], region: "Fès-Meknès"}
            ],
            airports: [
                {name: "Mohammed V", coords: [33.3675, -7.5897], radius: 5000, city: "Casablanca"},
                {name: "Rabat-Salé", coords: [34.0515, -6.7515], radius: 3000, city: "Rabat"}
            ]
        };
        
        function testMap() {
            try {
                log('Début du test de la carte', 'info');
                updateStatus('Test en cours...', 'warning');
                
                // Vérifier Leaflet
                if (typeof L === 'undefined') {
                    throw new Error('Leaflet non chargé');
                }
                log('✓ Leaflet chargé - Version: ' + L.version, 'success');
                
                // Créer la carte
                map = L.map('map', {
                    center: MOROCCO_CONFIG.center,
                    zoom: 6,
                    minZoom: 4,
                    maxZoom: 18
                });
                log('✓ Objet carte créé', 'success');
                
                // Ajouter les tuiles
                const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors | Royaume du Maroc',
                    maxZoom: 18
                });
                
                osmLayer.addTo(map);
                log('✓ Couche de tuiles ajoutée', 'success');
                
                // Ajouter les villes
                MOROCCO_CONFIG.cities.forEach(city => {
                    const icon = L.divIcon({
                        className: 'city-marker',
                        html: `<div class="city-icon ${city.capital ? 'capital' : ''}">${city.name}</div>`,
                        iconSize: [city.name.length * 8 + 20, 25],
                        iconAnchor: [city.name.length * 4 + 10, 12]
                    });
                    
                    const marker = L.marker(city.coords, {icon: icon}).addTo(map);
                    marker.bindPopup(`
                        <div>
                            <h6><strong>${city.name}</strong></h6>
                            ${city.capital ? '<p class="text-danger mb-1"><i class="fas fa-crown"></i> Capitale du Royaume</p>' : ''}
                            <p class="mb-1"><strong>Région:</strong> ${city.region}</p>
                        </div>
                    `);
                });
                log('✓ Marqueurs de villes ajoutés', 'success');
                
                // Ajouter les zones restreintes
                MOROCCO_CONFIG.airports.forEach(airport => {
                    const circle = L.circle(airport.coords, {
                        color: '#e74c3c',
                        fillColor: '#e74c3c',
                        fillOpacity: 0.2,
                        radius: airport.radius
                    }).addTo(map);
                    
                    circle.bindPopup(`
                        <div>
                            <h6 class="text-danger"><i class="fas fa-plane"></i> Zone Restreinte</h6>
                            <p><strong>Aéroport ${airport.name}</strong></p>
                            <p>Ville: ${airport.city}</p>
                        </div>
                    `);
                });
                log('✓ Zones restreintes ajoutées', 'success');
                
                // Événements de la carte
                map.whenReady(function() {
                    log('✓ Carte prête', 'success');
                    updateStatus('Carte opérationnelle !', 'success');
                });
                
                osmLayer.on('load', function() {
                    log('✓ Tuiles chargées', 'success');
                });
                
                osmLayer.on('tileerror', function(e) {
                    log('✗ Erreur de chargement des tuiles', 'error');
                });
                
                log('✓ Test terminé avec succès', 'success');
                
            } catch (error) {
                log('✗ Erreur: ' + error.message, 'error');
                updateStatus('Erreur: ' + error.message, 'danger');
                console.error('Erreur complète:', error);
            }
        }
        
        // Initialisation automatique
        document.addEventListener('DOMContentLoaded', function() {
            log('Page chargée', 'info');
            updateStatus('Prêt pour le test', 'info');
            
            // Test automatique après 1 seconde
            setTimeout(testMap, 1000);
        });
    </script>
</body>
</html>
