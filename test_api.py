#!/usr/bin/env python3
"""
Script de test pour l'API de surveillance des drones
Teste toutes les fonctionnalités principales de l'application
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000"
API_BASE = f"{BASE_URL}/api"

def test_api_endpoint(method, endpoint, data=None, expected_status=200):
    """Tester un endpoint de l'API"""
    url = f"{API_BASE}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url)
        elif method.upper() == "POST":
            response = requests.post(url, json=data)
        else:
            print(f"❌ Méthode {method} non supportée")
            return False
        
        if response.status_code == expected_status:
            print(f"✅ {method} {endpoint} - Status: {response.status_code}")
            return response.json() if response.content else True
        else:
            print(f"❌ {method} {endpoint} - Status: {response.status_code}, Expected: {expected_status}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Impossible de se connecter à {url}")
        print("   Assurez-vous que l'application est démarrée avec: python app_simple.py")
        return False
    except Exception as e:
        print(f"❌ Erreur lors du test de {endpoint}: {e}")
        return False

def test_drone_specifications():
    """Tester la récupération des spécifications des drones"""
    print("\n🔧 Test des spécifications des drones")
    print("=" * 50)
    
    # Test Wander-B
    wander_specs = test_api_endpoint("GET", "/drone-specs/Wander-B")
    if wander_specs:
        specs = wander_specs['specifications']
        print(f"   Wander-B - Autonomie: {specs['max_flight_time']}min, Vitesse: {specs['max_speed']}km/h")
    
    # Test Thunder-B
    thunder_specs = test_api_endpoint("GET", "/drone-specs/Thunder-B")
    if thunder_specs:
        specs = thunder_specs['specifications']
        print(f"   Thunder-B - Autonomie: {specs['max_flight_time']}min, Vitesse: {specs['max_speed']}km/h")
    
    # Test type invalide
    test_api_endpoint("GET", "/drone-specs/InvalidType", expected_status=400)

def test_drone_flight():
    """Tester le démarrage et l'arrêt d'un vol de drone"""
    print("\n🚁 Test des vols de drones")
    print("=" * 50)
    
    # Coordonnées de test (zone autour de Rabat)
    test_coordinates = [
        [34.0209, -6.8416],  # Rabat
        [34.0300, -6.8300],  # Point 2
        [34.0100, -6.8300],  # Point 3
        [34.0100, -6.8500]   # Point 4
    ]
    
    # Test démarrage Wander-B
    flight_data = {
        "drone_type": "Wander-B",
        "coordinates": test_coordinates,
        "duration": 30
    }
    
    result = test_api_endpoint("POST", "/start_drone", flight_data)
    if result:
        drone_id = result['drone_id']
        print(f"   Drone démarré avec ID: {drone_id[:8]}...")
        
        # Attendre un peu
        time.sleep(1)
        
        # Vérifier les drones actifs
        active_drones = test_api_endpoint("GET", "/drones/active")
        if active_drones:
            print(f"   Drones actifs: {len(active_drones)}")
        
        # Arrêter le drone
        stop_result = test_api_endpoint("POST", f"/drones/{drone_id}/stop")
        if stop_result:
            print(f"   Drone {drone_id[:8]}... arrêté avec succès")
    
    # Test durée trop longue
    invalid_flight_data = {
        "drone_type": "Wander-B",
        "coordinates": test_coordinates,
        "duration": 200  # Trop long pour Wander-B (max 90min)
    }
    test_api_endpoint("POST", "/start_drone", invalid_flight_data, expected_status=400)

def test_incident_management():
    """Tester la gestion des incidents"""
    print("\n⚠️  Test de la gestion des incidents")
    print("=" * 50)
    
    # Créer un incident météo
    incident_data = {
        "type": "weather",
        "description": "Conditions météorologiques défavorables détectées",
        "location": {"lat": 34.0209, "lng": -6.8416}
    }
    
    incident = test_api_endpoint("POST", "/incidents", incident_data)
    if incident:
        print(f"   Incident créé: {incident['type']} - {incident['description']}")
    
    # Vérifier les incidents actifs
    active_incidents = test_api_endpoint("GET", "/incidents/active")
    if active_incidents:
        print(f"   Incidents actifs: {len(active_incidents)}")
    
    # Tenter de démarrer un vol avec incident actif
    flight_data = {
        "drone_type": "Thunder-B",
        "coordinates": [[34.0209, -6.8416], [34.0300, -6.8300], [34.0100, -6.8300], [34.0100, -6.8500]],
        "duration": 60
    }
    test_api_endpoint("POST", "/start_drone", flight_data, expected_status=403)

def test_security_validations():
    """Tester les validations de sécurité"""
    print("\n🔒 Test des validations de sécurité")
    print("=" * 50)
    
    # Test paramètres manquants
    test_api_endpoint("POST", "/start_drone", {}, expected_status=400)
    
    # Test type de drone invalide
    invalid_data = {
        "drone_type": "InvalidDrone",
        "coordinates": [[34.0209, -6.8416]],
        "duration": 30
    }
    test_api_endpoint("POST", "/start_drone", invalid_data, expected_status=400)
    
    # Test arrêt de drone inexistant
    test_api_endpoint("POST", "/drones/nonexistent-id/stop", expected_status=404)

def test_application_health():
    """Tester la santé générale de l'application"""
    print("\n💚 Test de santé de l'application")
    print("=" * 50)
    
    try:
        # Test page principale
        response = requests.get(BASE_URL)
        if response.status_code == 200:
            print("✅ Page principale accessible")
            if "Royaume du Maroc" in response.text:
                print("✅ Contenu marocain détecté")
            else:
                print("⚠️  Contenu marocain non détecté")
        else:
            print(f"❌ Page principale inaccessible - Status: {response.status_code}")
    
    except Exception as e:
        print(f"❌ Erreur lors du test de santé: {e}")

def run_all_tests():
    """Exécuter tous les tests"""
    print("🇲🇦 TESTS DE L'APPLICATION DE SURVEILLANCE DES DRONES")
    print("=" * 60)
    print(f"Heure de début: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"URL de base: {BASE_URL}")
    
    # Tests de santé
    test_application_health()
    
    # Tests des spécifications
    test_drone_specifications()
    
    # Tests de sécurité
    test_security_validations()
    
    # Tests des vols
    test_drone_flight()
    
    # Tests des incidents
    test_incident_management()
    
    print("\n" + "=" * 60)
    print("🎯 TESTS TERMINÉS")
    print("=" * 60)
    print("\n📋 Résumé des fonctionnalités testées:")
    print("   ✅ Spécifications des drones (Wander-B et Thunder-B)")
    print("   ✅ Démarrage et arrêt des vols")
    print("   ✅ Validation des paramètres de sécurité")
    print("   ✅ Gestion des incidents")
    print("   ✅ Restrictions de vol en cas d'incident")
    print("   ✅ API REST complète")
    print("\n🌐 Pour tester l'interface web, ouvrez:")
    print(f"   {BASE_URL}")

if __name__ == "__main__":
    run_all_tests()
