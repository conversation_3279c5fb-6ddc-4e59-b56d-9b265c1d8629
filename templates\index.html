<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Suivi des Drones</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css" />
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .point-marker {
            background-color: #fff;
            border: 2px solid #2ecc71;
            border-radius: 50%;
            width: 12px;
            height: 12px;
            box-shadow: 0 0 10px rgba(46, 204, 113, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .point-marker:hover {
            transform: scale(1.2);
            box-shadow: 0 0 15px rgba(46, 204, 113, 0.8);
        }

        .point-marker.active {
            background-color: #2ecc71;
            box-shadow: 0 0 20px rgba(46, 204, 113, 1);
        }

        .drawing-tooltip {
            background-color: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            padding: 8px 12px;
            font-size: 14px;
            color: #2c3e50;
        }

        .drawing-tooltip:before {
            border-top-color: rgba(255, 255, 255, 0.9);
        }

        .polygon-area {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 8px 12px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            font-size: 14px;
            color: #2c3e50;
            display: none;
        }

        .drone-container {
            position: relative;
            width: 40px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .drone-icon {
            width: 40px;
            height: 40px;
            transform-origin: center;
            animation: droneFloat 2s infinite ease-in-out;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .drone-svg {
            width: 100%;
            height: 100%;
            filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));
        }

        .drone-body {
            fill: #2ecc71;
            stroke: #27ae60;
            stroke-width: 0.5;
        }

        .drone-wing {
            fill: #2ecc71;
            stroke: #27ae60;
            stroke-width: 0.5;
        }

        .drone-tail {
            fill: #2ecc71;
            stroke: #27ae60;
            stroke-width: 0.5;
        }

        .drone-light {
            fill: #e74c3c;
            filter: drop-shadow(0 0 3px rgba(231, 76, 60, 0.6));
        }

        @keyframes droneFloat {
            0%, 100% { 
                transform: translateY(0) rotate(0deg);
            }
            50% { 
                transform: translateY(-5px) rotate(2deg);
            }
        }

        .drone-shadow {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 50%;
            width: 30px;
            height: 8px;
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            filter: blur(3px);
            animation: shadowFloat 2s infinite ease-in-out;
        }

        @keyframes shadowFloat {
            0%, 100% { 
                transform: translateX(-50%) scale(1);
                opacity: 0.2;
            }
            50% { 
                transform: translateX(-50%) scale(0.8);
                opacity: 0.1;
            }
        }

        .drone-trail {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(46, 204, 113, 0.5);
            border-radius: 50%;
            pointer-events: none;
            animation: trailFade 2s forwards;
        }

        @keyframes trailFade {
            0% { 
                transform: scale(1);
                opacity: 0.5;
            }
            100% { 
                transform: scale(0);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 sidebar">
                <h2 class="text-center mb-4">Contrôle des Drones</h2>
                
                <!-- Sélection du type de drone -->
                <div class="mb-4">
                    <label class="form-label">Type de Drone</label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="droneType" id="wanderB" value="Wander-B" checked>
                        <label class="btn btn-outline-primary" for="wanderB">Wander-B</label>
                        
                        <input type="radio" class="btn-check" name="droneType" id="thunderB" value="Thunder-B">
                        <label class="btn btn-outline-primary" for="thunderB">Thunder-B</label>
                    </div>
                </div>

                <!-- Durée du vol -->
                <div class="mb-4">
                    <label class="form-label">Durée du vol (minutes)</label>
                    <input type="number" class="form-control" id="flightDuration" min="1" value="30">
                </div>

                <!-- Minuteur -->
                <div class="mb-4">
                    <div id="flightTimer" class="timer-display"></div>
                </div>

                <!-- Boutons d'action -->
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" id="startDrawing">Dessiner un polygone</button>
                    <button class="btn btn-success" id="startFlight">Démarrer le vol</button>
                    <button class="btn btn-danger" id="stopFlight" disabled>Arrêter le vol</button>
                    <button class="btn btn-secondary" id="clearMap">Effacer la carte</button>
                </div>

                <!-- Instructions -->
                <div class="mt-4">
                    <h5>Instructions :</h5>
                    <ul class="list-unstyled">
                        <li>• Cliquez sur "Dessiner un polygone"</li>
                        <li>• Cliquez 4 fois sur la carte pour créer les points</li>
                        <li>• Clic droit pour fermer le polygone</li>
                        <li>• Cliquez sur "Démarrer le vol" pour lancer le drone</li>
                    </ul>
                </div>
            </div>

            <!-- Carte -->
            <div class="col-md-9 p-0">
                <div id="map"></div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.js"></script>
    <script>
        // Variables globales
        let map;
        let drawnItems;
        let droneMarkers = new Map(); // Map pour stocker les drones et leurs polygones
        let flightIntervals = new Map(); // Map pour stocker les intervalles de vol
        let timerIntervals = new Map(); // Map pour stocker les intervalles de minuteur
        let remainingTimes = new Map(); // Map pour stocker les temps restants
        let isDrawing = false;
        let polygonPoints = [];
        let tempPolygon = null;
        let pointMarkers = [];
        let drawingTooltip = null;
        let areaDisplay = null;
        let dronePaths = new Map(); // Map pour stocker les chemins de vol
        let currentPathIndices = new Map(); // Map pour stocker les indices de chemin actuels
        let flightPaths = new Map(); // Map pour stocker les chemins de vol générés

        // Initialisation de la carte
        function initMap() {
            map = L.map('map').setView([48.8566, 2.3522], 13);
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            drawnItems = new L.FeatureGroup().addTo(map);
            
            // Créer l'affichage de la surface
            areaDisplay = L.control({position: 'topright'});
            areaDisplay.onAdd = function(map) {
                const div = L.DomUtil.create('div', 'polygon-area');
                div.innerHTML = 'Surface: 0 km²';
                return div;
            };
            areaDisplay.addTo(map);
        }

        // Fonction pour démarrer le dessin
        function startDrawing() {
            if (isDrawing) {
                stopDrawing();
                return;
            }
            
            isDrawing = true;
            polygonPoints = [];
            pointMarkers = [];
            
            // Ajouter les gestionnaires d'événements
            map.on('click', handleMapClick);
            map.on('contextmenu', handleRightClick);
            map.on('mousemove', handleMouseMove);
            
            // Mettre à jour l'interface
            document.getElementById('startDrawing').textContent = 'Annuler le dessin';
            document.getElementById('startDrawing').classList.remove('btn-primary');
            document.getElementById('startDrawing').classList.add('btn-warning');
        }

        // Fonction pour arrêter le dessin
        function stopDrawing() {
            isDrawing = false;
            polygonPoints = [];
            pointMarkers.forEach(marker => map.removeLayer(marker));
            pointMarkers = [];
            if (tempPolygon) {
                map.removeLayer(tempPolygon);
                tempPolygon = null;
            }
            if (drawingTooltip) {
                map.removeLayer(drawingTooltip);
                drawingTooltip = null;
            }
            
            // Retirer les gestionnaires d'événements
            map.off('click', handleMapClick);
            map.off('contextmenu', handleRightClick);
            map.off('mousemove', handleMouseMove);
            
            // Restaurer le bouton
            document.getElementById('startDrawing').textContent = 'Dessiner un polygone';
            document.getElementById('startDrawing').classList.remove('btn-warning');
            document.getElementById('startDrawing').classList.add('btn-primary');
        }

        // Gestionnaire de clic gauche
        function handleMapClick(e) {
            if (!isDrawing) return;
            
            const point = [e.latlng.lat, e.latlng.lng];
            polygonPoints.push(point);
            
            // Créer un marqueur pour le point
            const marker = L.divIcon({
                className: 'point-marker',
                iconSize: [12, 12]
            });
            
            const pointMarker = L.marker(e.latlng, {icon: marker}).addTo(map);
            pointMarkers.push(pointMarker);
            
            // Mettre à jour le polygone temporaire
            updateTempPolygon();
            
            // Mettre à jour le tooltip
            updateDrawingTooltip(e.latlng);
        }

        // Gestionnaire de mouvement de la souris
        function handleMouseMove(e) {
            if (!isDrawing || polygonPoints.length === 0) return;
            updateDrawingTooltip(e.latlng);
        }

        // Mise à jour du tooltip de dessin
        function updateDrawingTooltip(latlng) {
            if (!drawingTooltip) {
                drawingTooltip = L.tooltip({
                    permanent: true,
                    direction: 'top',
                    className: 'drawing-tooltip'
                }).addTo(map);
            }
            
            const pointsLeft = 4 - polygonPoints.length;
            drawingTooltip.setContent(`Points restants: ${pointsLeft}`);
            drawingTooltip.setLatLng(latlng);
        }

        // Gestionnaire de clic droit
        function handleRightClick(e) {
            e.originalEvent.preventDefault();
            
            if (!isDrawing || polygonPoints.length < 3) {
                alert('Vous devez placer au moins 3 points avant de fermer le polygone');
                return;
            }
            
            // Fermer le polygone
            polygonPoints.push(polygonPoints[0]);
            
            // Créer le polygone final avec un style moderne
            const polygon = L.polygon(polygonPoints, {
                color: '#2ecc71',
                weight: 2,
                fillColor: '#2ecc71',
                fillOpacity: 0.2,
                dashArray: '5, 5',
                lineJoin: 'round'
            }).addTo(map);
            
            // Générer un ID unique pour ce polygone
            const polygonId = 'polygon_' + Date.now();
            polygon.polygonId = polygonId;
            
            // Ajouter un effet de pulsation
            polygon.on('add', function() {
                const originalStyle = polygon.options;
                let opacity = 0.2;
                let increasing = true;
                
                const pulse = setInterval(() => {
                    if (increasing) {
                        opacity += 0.02;
                        if (opacity >= 0.4) increasing = false;
                    } else {
                        opacity -= 0.02;
                        if (opacity <= 0.2) increasing = true;
                    }
                    polygon.setStyle({fillOpacity: opacity});
                }, 50);
                
                setTimeout(() => clearInterval(pulse), 2000);
            });
            
            // Calculer et afficher la surface
            const area = L.GeometryUtil.geodesicArea(polygonPoints);
            const areaKm2 = (area / 1000000).toFixed(2);
            
            // Créer un affichage de surface pour ce polygone
            const areaDisplay = L.control({position: 'topright'});
            areaDisplay.onAdd = function(map) {
                const div = L.DomUtil.create('div', 'polygon-area');
                div.id = 'area_' + polygonId;
                div.innerHTML = `Surface ${polygonId}: ${areaKm2} km²`;
                return div;
            };
            areaDisplay.addTo(map);
            
            // Ajouter le polygone à la carte
            drawnItems.addLayer(polygon);
            
            // Arrêter le mode dessin
            stopDrawing();
        }

        // Mise à jour du polygone temporaire
        function updateTempPolygon() {
            if (tempPolygon) {
                map.removeLayer(tempPolygon);
            }
            
            if (polygonPoints.length >= 2) {
                const points = [...polygonPoints];
                if (polygonPoints.length >= 3) {
                    points.push(polygonPoints[0]);
                }
                
                tempPolygon = L.polyline(points, {
                    color: '#2ecc71',
                    weight: 2,
                    dashArray: '5, 5',
                    lineJoin: 'round'
                }).addTo(map);
            }
        }

        // Fonction pour créer l'icône du drone
        function createDroneIcon() {
            const container = document.createElement('div');
            container.className = 'drone-container';
            
            const drone = document.createElement('div');
            drone.className = 'drone-icon';
            
            // SVG simple du drone
            drone.innerHTML = `
                <svg class="drone-svg" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <!-- Corps du drone -->
                    <path class="drone-body" d="M4,12 L20,12 L18,14 L6,14 Z"/>
                    
                    <!-- Ailes -->
                    <path class="drone-wing" d="M8,12 L8,8 L16,8 L16,12 Z"/>
                    
                    <!-- Queue -->
                    <path class="drone-tail" d="M18,12 L20,10 L20,14 Z"/>
                    
                    <!-- Lumières -->
                    <circle class="drone-light" cx="4" cy="13" r="0.4"/>
                    <circle class="drone-light" cx="20" cy="12" r="0.4"/>
                </svg>
            `;
            
            const shadow = document.createElement('div');
            shadow.className = 'drone-shadow';
            
            container.appendChild(drone);
            container.appendChild(shadow);
            
            return L.divIcon({
                html: container.outerHTML,
                className: 'custom-drone-icon',
                iconSize: [40, 40],
                iconAnchor: [20, 20]
            });
        }

        // Fonction pour générer des points aléatoires dans le polygone
        function generateFlightPath(polygon) {
            const bounds = polygon.getBounds();
            const points = [];
            const numPoints = 20; // Nombre de points dans le chemin

            for (let i = 0; i < numPoints; i++) {
                let point;
                do {
                    const lat = bounds.getSouth() + Math.random() * (bounds.getNorth() - bounds.getSouth());
                    const lng = bounds.getWest() + Math.random() * (bounds.getEast() - bounds.getWest());
                    point = [lat, lng];
                } while (!isPointInPolygon(point, polygon.getLatLngs()[0]));

                points.push(point);
            }

            // Ajouter le point de départ à la fin pour créer une boucle
            points.push(points[0]);
            return points;
        }

        // Fonction pour vérifier si un point est dans le polygone
        function isPointInPolygon(point, polygon) {
            let inside = false;
            for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
                const xi = polygon[i].lat, yi = polygon[i].lng;
                const xj = polygon[j].lat, yj = polygon[j].lng;
                
                const intersect = ((yi > point[1]) !== (yj > point[1]))
                    && (point[0] < (xj - xi) * (point[1] - yi) / (yj - yi) + xi);
                if (intersect) inside = !inside;
            }
            return inside;
        }

        // Fonction pour démarrer le vol du drone
        function startDroneFlight(polygonId) {
            const polygon = Array.from(drawnItems.getLayers()).find(layer => layer.polygonId === polygonId);
            if (!polygon) return;

            if (droneMarkers.has(polygonId)) return;

            const flightPath = generateFlightPath(polygon);
            flightPaths.set(polygonId, flightPath);
            currentPathIndices.set(polygonId, 0);

            // Créer l'icône du drone
            const icon = createDroneIcon();
            const droneMarker = L.marker(flightPath[0], {
                icon: icon,
                zIndexOffset: 1000
            }).addTo(map);
            droneMarkers.set(polygonId, droneMarker);

            // Créer le chemin de vol
            const dronePath = L.polyline([], {
                color: '#e74c3c',
                weight: 2,
                dashArray: '5, 5',
                opacity: 0.5
            }).addTo(map);
            dronePaths.set(polygonId, dronePath);

            // Démarrer l'animation
            animateDrone(polygonId);
        }

        // Fonction pour animer le drone
        function animateDrone(polygonId) {
            if (!droneMarkers.has(polygonId)) return;

            const currentPoint = flightPaths.get(polygonId)[currentPathIndices.get(polygonId)];
            const nextPoint = flightPaths.get(polygonId)[(currentPathIndices.get(polygonId) + 1) % flightPaths.get(polygonId).length];
            
            // Calculer la direction du drone
            const angle = Math.atan2(nextPoint[1] - currentPoint[1], nextPoint[0] - currentPoint[0]) * 180 / Math.PI;
            
            // Mettre à jour la position et la rotation du drone
            const droneMarker = droneMarkers.get(polygonId);
            droneMarker.setLatLng(currentPoint);
            const droneElement = droneMarker.getElement();
            if (droneElement) {
                const droneIconElement = droneElement.querySelector('.drone-icon');
                if (droneIconElement) {
                    droneIconElement.style.transform = `rotate(${angle}deg)`;
                }
            }

            // Mettre à jour le chemin
            const dronePath = dronePaths.get(polygonId);
            dronePath.addLatLng(currentPoint);

            // Passer au point suivant
            currentPathIndices.set(polygonId, (currentPathIndices.get(polygonId) + 1) % flightPaths.get(polygonId).length);

            // Continuer l'animation
            setTimeout(() => animateDrone(polygonId), 1000);
        }

        // Fonction pour arrêter le vol du drone
        function stopDroneFlight(polygonId) {
            if (droneMarkers.has(polygonId)) {
                const droneMarker = droneMarkers.get(polygonId);
                map.removeLayer(droneMarker);
                droneMarkers.delete(polygonId);
            }
            if (dronePaths.has(polygonId)) {
                const dronePath = dronePaths.get(polygonId);
                map.removeLayer(dronePath);
                dronePaths.delete(polygonId);
            }
            flightPaths.delete(polygonId);
            currentPathIndices.delete(polygonId);
        }

        // Modifier la fonction startFlight
        function startFlight() {
            const polygons = Array.from(drawnItems.getLayers());
            if (polygons.length === 0) {
                alert('Veuillez d\'abord dessiner une zone de vol!');
                return;
            }

            const duration = parseInt(document.getElementById('flightDuration').value);
            if (isNaN(duration) || duration <= 0) {
                alert('Veuillez entrer une durée de vol valide');
                return;
            }

            document.getElementById('stopFlight').disabled = false;
            document.getElementById('startFlight').disabled = true;

            // Démarrer le vol pour chaque polygone
            polygons.forEach(polygon => {
                const polygonId = polygon.polygonId;
                remainingTimes.set(polygonId, duration * 60);
                updateTimerDisplay(polygonId);
                startDroneFlight(polygonId);

                // Démarrer le minuteur pour ce polygone
                const timerInterval = setInterval(() => {
                    const remainingTime = remainingTimes.get(polygonId) - 1;
                    remainingTimes.set(polygonId, remainingTime);
                    updateTimerDisplay(polygonId);

                    if (remainingTime <= 0) {
                        stopFlightForPolygon(polygonId);
                    }
                }, 1000);
                timerIntervals.set(polygonId, timerInterval);
            });
        }

        // Ajouter une nouvelle fonction pour arrêter le vol d'un polygone spécifique
        function stopFlightForPolygon(polygonId) {
            clearInterval(timerIntervals.get(polygonId));
            timerIntervals.delete(polygonId);
            stopDroneFlight(polygonId);
            remainingTimes.delete(polygonId);
            
            // Vérifier si tous les vols sont arrêtés
            if (timerIntervals.size === 0) {
                document.getElementById('stopFlight').disabled = true;
                document.getElementById('startFlight').disabled = false;
            }
        }

        // Modifier la fonction stopFlight
        function stopFlight() {
            // Arrêter tous les vols
            Array.from(timerIntervals.keys()).forEach(polygonId => {
                stopFlightForPolygon(polygonId);
            });
            
            document.getElementById('stopFlight').disabled = true;
            document.getElementById('startFlight').disabled = false;
        }

        // Modifier la fonction updateTimerDisplay
        function updateTimerDisplay(polygonId) {
            const remainingTime = remainingTimes.get(polygonId);
            const minutes = Math.floor(remainingTime / 60);
            const seconds = remainingTime % 60;
            const timerDisplay = document.getElementById('timer_' + polygonId);
            if (timerDisplay) {
                timerDisplay.textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }

        // Modifier la fonction clearMap
        function clearMap() {
            // Arrêter tous les vols
            Array.from(timerIntervals.keys()).forEach(polygonId => {
                stopFlightForPolygon(polygonId);
            });
            
            drawnItems.clearLayers();
            if (isDrawing) {
                stopDrawing();
            }
            
            // Supprimer tous les affichages de surface
            document.querySelectorAll('.polygon-area').forEach(el => el.remove());
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', () => {
            initMap();
            document.getElementById('startDrawing').addEventListener('click', startDrawing);
            document.getElementById('startFlight').addEventListener('click', startFlight);
            document.getElementById('stopFlight').addEventListener('click', stopFlight);
            document.getElementById('clearMap').addEventListener('click', clearMap);
        });
    </script>
</body>
</html> 