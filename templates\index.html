<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surveillance des Drones - Royaume du Maroc</title>
    
    <!-- CSS Libraries -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    <style>
        /* Styles spécifiques pour les drones */
        .drone-container {
            position: relative;
            width: 40px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .drone-icon {
            width: 40px;
            height: 40px;
            transform-origin: center;
            animation: droneFloat 2s infinite ease-in-out;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .drone-svg {
            width: 100%;
            height: 100%;
            filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));
        }

        @keyframes droneFloat {
            0%, 100% { 
                transform: translateY(0) rotate(0deg);
            }
            50% { 
                transform: translateY(-5px) rotate(2deg);
            }
        }

        .drone-shadow {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 50%;
            width: 30px;
            height: 8px;
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            filter: blur(3px);
            animation: shadowFloat 2s infinite ease-in-out;
        }

        @keyframes shadowFloat {
            0%, 100% { 
                transform: translateX(-50%) scale(1);
                opacity: 0.2;
            }
            50% { 
                transform: translateX(-50%) scale(0.8);
                opacity: 0.1;
            }
        }

        /* Styles pour les marqueurs de points */
        .point-marker {
            background-color: #fff;
            border: 2px solid #2ecc71;
            border-radius: 50%;
            width: 12px;
            height: 12px;
            box-shadow: 0 0 10px rgba(46, 204, 113, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .point-marker:hover {
            transform: scale(1.2);
            box-shadow: 0 0 15px rgba(46, 204, 113, 0.8);
        }

        .point-marker.active {
            background-color: #2ecc71;
            box-shadow: 0 0 20px rgba(46, 204, 113, 1);
        }

        /* Styles pour les tooltips de dessin */
        .drawing-tooltip {
            background-color: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            padding: 8px 12px;
            font-size: 14px;
            color: #2c3e50;
        }

        .drawing-tooltip:before {
            border-top-color: rgba(255, 255, 255, 0.9);
        }

        /* Styles pour l'affichage de surface */
        .polygon-area {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 8px 12px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            font-size: 14px;
            color: #2c3e50;
            display: none;
        }

        /* Header avec drapeau marocain */
        .morocco-header {
            background: linear-gradient(135deg, #c1272d 0%, #006233 50%, #c1272d 100%);
            color: white;
            padding: 10px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        .morocco-flag {
            display: inline-block;
            margin-right: 10px;
            font-size: 1.2em;
        }

        /* Styles pour les zones sensibles */
        .sensitive-zone {
            fill: rgba(231, 76, 60, 0.3);
            stroke: #e74c3c;
            stroke-width: 2;
            stroke-dasharray: 10, 5;
            animation: zonePulse 3s infinite;
        }

        @keyframes zonePulse {
            0%, 100% { 
                fill-opacity: 0.2; 
                stroke-opacity: 1;
            }
            50% { 
                fill-opacity: 0.4; 
                stroke-opacity: 0.7;
            }
        }
    </style>
</head>
<body>
    <!-- Header avec identité marocaine -->
    <div class="morocco-header">
        <div class="container-fluid">
            <h4 class="mb-0">
                <span class="morocco-flag">🇲🇦</span>
                Système de Surveillance des Drones - Royaume du Maroc
                <small class="ms-3">المملكة المغربية - نظام مراقبة الطائرات بدون طيار</small>
            </h4>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar de contrôle -->
            <div class="col-md-3 sidebar">
                <h5 class="text-center mb-4">
                    <i class="fas fa-drone me-2"></i>
                    Contrôle des Drones
                </h5>
                
                <!-- Sélection du type de drone -->
                <div class="mb-4">
                    <label class="form-label">
                        <i class="fas fa-helicopter me-1"></i>
                        Type de Drone
                    </label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="droneType" id="wanderB" value="Wander-B" checked>
                        <label class="btn btn-outline-primary" for="wanderB">
                            <i class="fas fa-eye me-1"></i>
                            Wander-B
                        </label>
                        
                        <input type="radio" class="btn-check" name="droneType" id="thunderB" value="Thunder-B">
                        <label class="btn btn-outline-primary" for="thunderB">
                            <i class="fas fa-shield-alt me-1"></i>
                            Thunder-B
                        </label>
                    </div>
                    
                    <!-- Spécifications du drone sélectionné -->
                    <div id="droneSpecs" class="mt-3 p-3 bg-light rounded">
                        <h6><i class="fas fa-cogs me-1"></i> Spécifications:</h6>
                        <div id="specsContent">
                            <small class="text-muted">Chargement des spécifications...</small>
                        </div>
                    </div>
                </div>

                <!-- Durée du vol -->
                <div class="mb-4">
                    <label class="form-label">
                        <i class="fas fa-clock me-1"></i>
                        Durée du vol (minutes)
                    </label>
                    <input type="number" class="form-control" id="flightDuration" min="1" max="180" value="30">
                    <small class="text-muted">Maximum selon le type de drone</small>
                </div>

                <!-- État du système -->
                <div class="mb-4">
                    <h6><i class="fas fa-heartbeat me-1"></i> État du Système</h6>
                    <div id="systemStatus" class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle"></i> Système opérationnel
                    </div>
                    
                    <!-- Incidents actifs -->
                    <div id="activeIncidents" class="mt-2" style="display: none;">
                        <h6 class="text-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            Incidents Actifs:
                        </h6>
                        <div id="incidentsList"></div>
                    </div>
                </div>

                <!-- Drones actifs -->
                <div class="mb-4">
                    <h6><i class="fas fa-plane me-1"></i> Drones Actifs</h6>
                    <div id="activeDronesList" class="list-group">
                        <div class="list-group-item text-muted text-center">
                            <i class="fas fa-info-circle me-1"></i>
                            Aucun drone en vol
                        </div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="d-grid gap-2 mb-4">
                    <button class="btn btn-primary" id="startDrawing">
                        <i class="fas fa-draw-polygon me-1"></i>
                        Dessiner une zone
                    </button>
                    <button class="btn btn-success" id="startFlight">
                        <i class="fas fa-play me-1"></i>
                        Démarrer le vol
                    </button>
                    <button class="btn btn-danger" id="stopFlight" disabled>
                        <i class="fas fa-stop me-1"></i>
                        Arrêter le vol
                    </button>
                    <button class="btn btn-secondary" id="clearMap">
                        <i class="fas fa-eraser me-1"></i>
                        Effacer la carte
                    </button>
                </div>

                <!-- Contrôles d'urgence -->
                <div class="mb-4">
                    <h6 class="text-danger">
                        <i class="fas fa-exclamation-circle me-1"></i>
                        Contrôles d'Urgence
                    </h6>
                    <div class="d-grid gap-2">
                        <button class="btn emergency-btn" id="emergencyLanding">
                            <i class="fas fa-parachute-box me-1"></i>
                            Atterrissage d'urgence
                        </button>
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-warning btn-sm" id="simulateWeather" title="Simuler incident météo">
                                <i class="fas fa-cloud-rain"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm" id="simulateSecurity" title="Simuler incident sécurité">
                                <i class="fas fa-shield-alt"></i>
                            </button>
                            <button class="btn btn-outline-info btn-sm" id="simulateTechnical" title="Simuler incident technique">
                                <i class="fas fa-wrench"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="mt-4">
                    <h6><i class="fas fa-info-circle me-1"></i> Instructions:</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-arrow-right me-1 text-primary"></i> Sélectionnez le type de drone</li>
                        <li><i class="fas fa-arrow-right me-1 text-primary"></i> Dessinez une zone de vol</li>
                        <li><i class="fas fa-arrow-right me-1 text-primary"></i> Définissez la durée</li>
                        <li><i class="fas fa-arrow-right me-1 text-primary"></i> Démarrez la surveillance</li>
                    </ul>
                </div>
            </div>

            <!-- Carte principale -->
            <div class="col-md-9 p-0">
                <div id="map"></div>
                
                <!-- Légende de la carte -->
                <div class="position-absolute bottom-0 start-0 m-3 bg-white p-2 rounded shadow-sm" style="z-index: 1000;">
                    <small class="fw-bold">Légende:</small><br>
                    <small><i class="fas fa-circle text-primary"></i> Wander-B (Reconnaissance)</small><br>
                    <small><i class="fas fa-circle text-danger"></i> Thunder-B (Surveillance)</small><br>
                    <small><i class="fas fa-circle" style="color: #e74c3c;"></i> Zones restreintes</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.js"></script>
    <script src="{{ url_for('static', filename='js/alerts.js') }}"></script>
    <script src="{{ url_for('static', filename='js/drone-system.js') }}"></script>

    <script>
        // Initialisation de l'application
        document.addEventListener('DOMContentLoaded', function() {
            // Initialiser la carte et les WebSockets
            initMap();
            initWebSocket();

            // Gestionnaires d'événements pour les types de drones
            document.querySelectorAll('input[name="droneType"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.checked) {
                        loadDroneSpecs(this.value);
                    }
                });
            });

            // Gestionnaires pour les boutons d'action
            document.getElementById('startDrawing').addEventListener('click', startDrawing);
            document.getElementById('startFlight').addEventListener('click', startFlight);
            document.getElementById('stopFlight').addEventListener('click', stopFlight);
            document.getElementById('clearMap').addEventListener('click', clearMap);

            // Gestionnaires pour les contrôles d'urgence
            document.getElementById('emergencyLanding').addEventListener('click', emergencyLandingAll);
            document.getElementById('simulateWeather').addEventListener('click', () => simulateIncident('weather'));
            document.getElementById('simulateSecurity').addEventListener('click', () => simulateIncident('security'));
            document.getElementById('simulateTechnical').addEventListener('click', () => simulateIncident('technical'));

            // Charger les spécifications du drone par défaut
            loadDroneSpecs('Wander-B');

            // Message de bienvenue
            setTimeout(() => {
                if (window.alertSystem) {
                    alertSystem.info('Bienvenue', 'Système de surveillance des drones du Royaume du Maroc', {
                        duration: 5000,
                        showProgress: true
                    });
                }
            }, 1000);
        });

        // Fonctions de gestion des événements (à implémenter)
        function startDrawing() {
            // TODO: Implémenter le dessin de polygones
            console.log('Démarrage du dessin');
        }

        function startFlight() {
            // TODO: Implémenter le démarrage de vol
            console.log('Démarrage du vol');
        }

        function stopFlight() {
            // TODO: Implémenter l'arrêt de vol
            console.log('Arrêt du vol');
        }

        function clearMap() {
            // TODO: Implémenter l'effacement de la carte
            console.log('Effacement de la carte');
        }

        function emergencyLandingAll() {
            // TODO: Implémenter l'atterrissage d'urgence
            console.log('Atterrissage d\'urgence');
        }

        function simulateIncident(type) {
            // TODO: Implémenter la simulation d'incidents
            console.log('Simulation d\'incident:', type);
        }

        function stopDroneById(droneId) {
            // TODO: Implémenter l'arrêt d'un drone spécifique
            console.log('Arrêt du drone:', droneId);
        }

        function updateDronePosition(data) {
            // TODO: Implémenter la mise à jour de position
            console.log('Mise à jour position drone:', data);
        }
    </script>
</body>
</html>
