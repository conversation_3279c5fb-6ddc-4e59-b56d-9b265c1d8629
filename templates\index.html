<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surveillance des Drones - Royaume du Maroc</title>
    
    <!-- CSS Libraries -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    <style>
        /* Styles pour la carte */
        #map {
            height: 100vh;
            width: 100%;
            z-index: 1;
        }

        .sidebar {
            height: 100vh;
            overflow-y: auto;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
            padding: 20px;
        }

        /* Styles spécifiques pour les drones */
        .drone-container {
            position: relative;
            width: 40px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .drone-icon {
            width: 40px;
            height: 40px;
            transform-origin: center;
            animation: droneFloat 2s infinite ease-in-out;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .drone-svg {
            width: 100%;
            height: 100%;
            filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));
        }

        @keyframes droneFloat {
            0%, 100% { 
                transform: translateY(0) rotate(0deg);
            }
            50% { 
                transform: translateY(-5px) rotate(2deg);
            }
        }

        .drone-shadow {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 50%;
            width: 30px;
            height: 8px;
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            filter: blur(3px);
            animation: shadowFloat 2s infinite ease-in-out;
        }

        @keyframes shadowFloat {
            0%, 100% { 
                transform: translateX(-50%) scale(1);
                opacity: 0.2;
            }
            50% { 
                transform: translateX(-50%) scale(0.8);
                opacity: 0.1;
            }
        }

        /* Styles pour les marqueurs de points */
        .point-marker {
            background-color: #fff;
            border: 2px solid #2ecc71;
            border-radius: 50%;
            width: 12px;
            height: 12px;
            box-shadow: 0 0 10px rgba(46, 204, 113, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .point-marker:hover {
            transform: scale(1.2);
            box-shadow: 0 0 15px rgba(46, 204, 113, 0.8);
        }

        .point-marker.active {
            background-color: #2ecc71;
            box-shadow: 0 0 20px rgba(46, 204, 113, 1);
        }

        /* Styles pour les tooltips de dessin */
        .drawing-tooltip {
            background-color: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            padding: 8px 12px;
            font-size: 14px;
            color: #2c3e50;
        }

        .drawing-tooltip:before {
            border-top-color: rgba(255, 255, 255, 0.9);
        }

        /* Styles pour l'affichage de surface */
        .polygon-area {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 8px 12px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            font-size: 14px;
            color: #2c3e50;
            display: none;
        }

        /* Header avec drapeau marocain */
        .morocco-header {
            background: linear-gradient(135deg, #c1272d 0%, #006233 50%, #c1272d 100%);
            color: white;
            padding: 10px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        .morocco-flag {
            display: inline-block;
            margin-right: 10px;
            font-size: 1.2em;
        }

        /* Styles pour les zones sensibles */
        .sensitive-zone {
            fill: rgba(231, 76, 60, 0.3);
            stroke: #e74c3c;
            stroke-width: 2;
            stroke-dasharray: 10, 5;
            animation: zonePulse 3s infinite;
        }

        @keyframes zonePulse {
            0%, 100% { 
                fill-opacity: 0.2; 
                stroke-opacity: 1;
            }
            50% { 
                fill-opacity: 0.4; 
                stroke-opacity: 0.7;
            }
        }
    </style>
</head>
<body>
    <!-- Header avec identité marocaine -->
    <div class="morocco-header">
        <div class="container-fluid">
            <h4 class="mb-0">
                <span class="morocco-flag">🇲🇦</span>
                Système de Surveillance des Drones - Royaume du Maroc
                <small class="ms-3">المملكة المغربية - نظام مراقبة الطائرات بدون طيار</small>
            </h4>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar de contrôle -->
            <div class="col-md-3 sidebar">
                <h5 class="text-center mb-4">
                    <i class="fas fa-drone me-2"></i>
                    Contrôle des Drones
                </h5>
                
                <!-- Sélection du type de drone -->
                <div class="mb-4">
                    <label class="form-label">
                        <i class="fas fa-helicopter me-1"></i>
                        Type de Drone
                    </label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="droneType" id="wanderB" value="Wander-B" checked>
                        <label class="btn btn-outline-primary" for="wanderB">
                            <i class="fas fa-eye me-1"></i>
                            Wander-B
                        </label>
                        
                        <input type="radio" class="btn-check" name="droneType" id="thunderB" value="Thunder-B">
                        <label class="btn btn-outline-primary" for="thunderB">
                            <i class="fas fa-shield-alt me-1"></i>
                            Thunder-B
                        </label>
                    </div>
                    
                    <!-- Spécifications du drone sélectionné -->
                    <div id="droneSpecs" class="mt-3 p-3 bg-light rounded">
                        <h6><i class="fas fa-cogs me-1"></i> Spécifications:</h6>
                        <div id="specsContent">
                            <small class="text-muted">Chargement des spécifications...</small>
                        </div>
                    </div>
                </div>

                <!-- Durée du vol -->
                <div class="mb-4">
                    <label class="form-label">
                        <i class="fas fa-clock me-1"></i>
                        Durée du vol (minutes)
                    </label>
                    <input type="number" class="form-control" id="flightDuration" min="1" max="180" value="30">
                    <small class="text-muted">Maximum selon le type de drone</small>
                </div>

                <!-- État du système -->
                <div class="mb-4">
                    <h6><i class="fas fa-heartbeat me-1"></i> État du Système</h6>
                    <div id="systemStatus" class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle"></i> Système opérationnel
                    </div>
                    
                    <!-- Incidents actifs -->
                    <div id="activeIncidents" class="mt-2" style="display: none;">
                        <h6 class="text-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            Incidents Actifs:
                        </h6>
                        <div id="incidentsList"></div>
                    </div>
                </div>

                <!-- Drones actifs -->
                <div class="mb-4">
                    <h6><i class="fas fa-plane me-1"></i> Drones Actifs</h6>
                    <div id="activeDronesList" class="list-group">
                        <div class="list-group-item text-muted text-center">
                            <i class="fas fa-info-circle me-1"></i>
                            Aucun drone en vol
                        </div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="d-grid gap-2 mb-4">
                    <button class="btn btn-primary" id="startDrawing">
                        <i class="fas fa-draw-polygon me-1"></i>
                        Dessiner une zone
                    </button>
                    <button class="btn btn-success" id="startFlight">
                        <i class="fas fa-play me-1"></i>
                        Démarrer le vol
                    </button>
                    <button class="btn btn-danger" id="stopFlight" disabled>
                        <i class="fas fa-stop me-1"></i>
                        Arrêter le vol
                    </button>
                    <button class="btn btn-secondary" id="clearMap">
                        <i class="fas fa-eraser me-1"></i>
                        Effacer la carte
                    </button>
                </div>

                <!-- Contrôles d'urgence -->
                <div class="mb-4">
                    <h6 class="text-danger">
                        <i class="fas fa-exclamation-circle me-1"></i>
                        Contrôles d'Urgence
                    </h6>
                    <div class="d-grid gap-2">
                        <button class="btn emergency-btn" id="emergencyLanding">
                            <i class="fas fa-parachute-box me-1"></i>
                            Atterrissage d'urgence
                        </button>
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-warning btn-sm" id="simulateWeather" title="Simuler incident météo">
                                <i class="fas fa-cloud-rain"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm" id="simulateSecurity" title="Simuler incident sécurité">
                                <i class="fas fa-shield-alt"></i>
                            </button>
                            <button class="btn btn-outline-info btn-sm" id="simulateTechnical" title="Simuler incident technique">
                                <i class="fas fa-wrench"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="mt-4">
                    <h6><i class="fas fa-info-circle me-1"></i> Instructions:</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-arrow-right me-1 text-primary"></i> Sélectionnez le type de drone</li>
                        <li><i class="fas fa-arrow-right me-1 text-primary"></i> Dessinez une zone de vol</li>
                        <li><i class="fas fa-arrow-right me-1 text-primary"></i> Définissez la durée</li>
                        <li><i class="fas fa-arrow-right me-1 text-primary"></i> Démarrez la surveillance</li>
                    </ul>
                </div>
            </div>

            <!-- Carte principale -->
            <div class="col-md-9 p-0">
                <div id="map"></div>
                
                <!-- Légende de la carte -->
                <div class="position-absolute bottom-0 start-0 m-3 bg-white p-2 rounded shadow-sm" style="z-index: 1000;">
                    <small class="fw-bold">Légende:</small><br>
                    <small><i class="fas fa-circle text-primary"></i> Wander-B (Reconnaissance)</small><br>
                    <small><i class="fas fa-circle text-danger"></i> Thunder-B (Surveillance)</small><br>
                    <small><i class="fas fa-circle" style="color: #e74c3c;"></i> Zones restreintes</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.js"></script>
    <script src="{{ url_for('static', filename='js/alerts.js') }}"></script>
    <script src="{{ url_for('static', filename='js/drone-system.js') }}"></script>

    <script>
        // Initialisation de l'application
        document.addEventListener('DOMContentLoaded', function() {
            // Initialiser la carte et les WebSockets
            initMap();
            initWebSocket();

            // Gestionnaires d'événements pour les types de drones
            document.querySelectorAll('input[name="droneType"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.checked) {
                        loadDroneSpecs(this.value);
                    }
                });
            });

            // Gestionnaires pour les boutons d'action
            document.getElementById('startDrawing').addEventListener('click', startDrawing);
            document.getElementById('startFlight').addEventListener('click', startFlight);
            document.getElementById('stopFlight').addEventListener('click', stopFlight);
            document.getElementById('clearMap').addEventListener('click', clearMap);

            // Gestionnaires pour les contrôles d'urgence
            document.getElementById('emergencyLanding').addEventListener('click', emergencyLandingAll);
            document.getElementById('simulateWeather').addEventListener('click', () => simulateIncident('weather'));
            document.getElementById('simulateSecurity').addEventListener('click', () => simulateIncident('security'));
            document.getElementById('simulateTechnical').addEventListener('click', () => simulateIncident('technical'));

            // Charger les spécifications du drone par défaut
            loadDroneSpecs('Wander-B');

            // Message de bienvenue
            setTimeout(() => {
                if (window.alertSystem) {
                    alertSystem.info('Bienvenue', 'Système de surveillance des drones du Royaume du Maroc', {
                        duration: 5000,
                        showProgress: true
                    });
                }
            }, 1000);
        });

        // Variables globales pour le dessin
        let isDrawing = false;
        let polygonPoints = [];
        let tempPolygon = null;
        let pointMarkers = [];

        // Fonction pour démarrer le dessin de polygones
        function startDrawing() {
            if (isDrawing) {
                alertSystem.warning('Dessin en cours', 'Terminez le polygone actuel avant d\'en commencer un nouveau');
                return;
            }

            isDrawing = true;
            polygonPoints = [];
            pointMarkers = [];

            document.getElementById('startDrawing').disabled = true;
            document.getElementById('startDrawing').innerHTML = '<i class="fas fa-hand-pointer me-1"></i> Cliquez sur la carte';

            // Ajouter l'écouteur de clic sur la carte
            map.on('click', onMapClick);

            alertSystem.info('Mode dessin activé', 'Cliquez sur la carte pour dessiner une zone de vol');
        }

        // Fonction pour gérer les clics sur la carte
        function onMapClick(e) {
            if (!isDrawing) return;

            const point = [e.latlng.lat, e.latlng.lng];
            polygonPoints.push(point);

            // Ajouter un marqueur pour le point
            const marker = L.circleMarker(e.latlng, {
                color: '#2ecc71',
                fillColor: '#2ecc71',
                fillOpacity: 0.8,
                radius: 6
            }).addTo(map);
            pointMarkers.push(marker);

            // Si on a au moins 3 points, créer/mettre à jour le polygone temporaire
            if (polygonPoints.length >= 3) {
                if (tempPolygon) {
                    map.removeLayer(tempPolygon);
                }

                tempPolygon = L.polygon(polygonPoints, {
                    color: '#3498db',
                    fillColor: '#3498db',
                    fillOpacity: 0.3,
                    weight: 2
                }).addTo(map);

                // Ajouter un popup pour finaliser
                tempPolygon.bindPopup(`
                    <div class="text-center">
                        <p><strong>Zone de vol</strong></p>
                        <p>Points: ${polygonPoints.length}</p>
                        <button class="btn btn-success btn-sm" onclick="finishDrawing()">
                            <i class="fas fa-check"></i> Finaliser
                        </button>
                        <button class="btn btn-danger btn-sm ms-1" onclick="cancelDrawing()">
                            <i class="fas fa-times"></i> Annuler
                        </button>
                    </div>
                `).openPopup();
            }

            alertSystem.info('Point ajouté', `${polygonPoints.length} point(s) - ${polygonPoints.length >= 3 ? 'Cliquez sur le popup pour finaliser' : 'Continuez à cliquer'}`);
        }

        // Fonction pour finaliser le dessin
        function finishDrawing() {
            if (polygonPoints.length < 3) {
                alertSystem.warning('Polygone incomplet', 'Il faut au moins 3 points pour créer une zone');
                return;
            }

            // Finaliser le polygone
            if (tempPolygon) {
                tempPolygon.closePopup();
                tempPolygon.setStyle({
                    color: '#27ae60',
                    fillColor: '#27ae60',
                    fillOpacity: 0.2,
                    weight: 3
                });

                // Ajouter le polygone au groupe des éléments dessinés
                drawnItems.addLayer(tempPolygon);
                tempPolygon.polygonId = 'polygon_' + Date.now();
            }

            // Nettoyer
            cleanupDrawing();

            alertSystem.success('Zone créée', 'Zone de vol créée avec succès');
        }

        // Fonction pour annuler le dessin
        function cancelDrawing() {
            cleanupDrawing();
            alertSystem.info('Dessin annulé', 'Le dessin de la zone a été annulé');
        }

        // Fonction pour nettoyer le mode dessin
        function cleanupDrawing() {
            isDrawing = false;
            map.off('click', onMapClick);

            // Supprimer les marqueurs de points
            pointMarkers.forEach(marker => map.removeLayer(marker));
            pointMarkers = [];

            // Réinitialiser le bouton
            document.getElementById('startDrawing').disabled = false;
            document.getElementById('startDrawing').innerHTML = '<i class="fas fa-draw-polygon me-1"></i> Dessiner une zone';

            polygonPoints = [];
            tempPolygon = null;
        }

        // Fonction pour démarrer un vol
        async function startFlight() {
            const polygons = Array.from(drawnItems.getLayers());
            if (polygons.length === 0) {
                alertSystem.warning('Aucune zone', 'Veuillez d\'abord dessiner une zone de vol');
                return;
            }

            const duration = parseInt(document.getElementById('flightDuration').value);
            if (isNaN(duration) || duration <= 0) {
                alertSystem.warning('Durée invalide', 'Veuillez entrer une durée de vol valide');
                return;
            }

            const droneType = document.querySelector('input[name="droneType"]:checked').value;

            // Vérifier si la durée est compatible avec le drone
            if (currentDroneSpecs && duration > currentDroneSpecs.max_flight_time) {
                alertSystem.warning('Durée trop longue',
                    `Maximum pour ${droneType}: ${currentDroneSpecs.max_flight_time} minutes`);
                return;
            }

            document.getElementById('stopFlight').disabled = false;
            document.getElementById('startFlight').disabled = true;

            // Démarrer le vol pour chaque polygone
            for (const polygon of polygons) {
                const coordinates = polygon.getLatLngs()[0].map(latlng => [latlng.lat, latlng.lng]);

                try {
                    const response = await fetch('/api/start_drone', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            drone_type: droneType,
                            coordinates: coordinates,
                            duration: duration
                        })
                    });

                    const data = await response.json();

                    if (response.ok) {
                        alertSystem.success('Vol démarré',
                            `${droneType} - ID: ${data.drone_id.substring(0, 8)}`);
                    } else {
                        alertSystem.danger('Erreur', data.error);
                        document.getElementById('stopFlight').disabled = true;
                        document.getElementById('startFlight').disabled = false;
                        return;
                    }
                } catch (error) {
                    console.error('Erreur lors du démarrage du vol:', error);
                    alertSystem.danger('Erreur réseau', 'Impossible de démarrer le vol');
                    document.getElementById('stopFlight').disabled = true;
                    document.getElementById('startFlight').disabled = false;
                    return;
                }
            }
        }

        // Fonction pour arrêter tous les vols
        function stopFlight() {
            // Arrêter tous les drones actifs
            activeDrones.forEach(async (drone, droneId) => {
                try {
                    await fetch(`/api/drones/${droneId}/stop`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                } catch (error) {
                    console.error('Erreur lors de l\'arrêt du drone:', error);
                }
            });

            document.getElementById('stopFlight').disabled = true;
            document.getElementById('startFlight').disabled = false;

            alertSystem.info('Vols arrêtés', 'Tous les vols ont été arrêtés');
        }

        // Fonction pour effacer la carte
        function clearMap() {
            if (confirm('Êtes-vous sûr de vouloir effacer toutes les zones dessinées ?')) {
                drawnItems.clearLayers();
                cleanupDrawing();
                alertSystem.info('Carte effacée', 'Toutes les zones ont été supprimées');
            }
        }

        // Fonction pour l'atterrissage d'urgence
        async function emergencyLandingAll() {
            if (activeDrones.size === 0) {
                alertSystem.info('Aucun drone', 'Aucun drone en vol actuellement');
                return;
            }

            if (confirm('Êtes-vous sûr de vouloir effectuer un atterrissage d\'urgence de tous les drones ?')) {
                try {
                    const droneIds = Array.from(activeDrones.keys());
                    for (const droneId of droneIds) {
                        await fetch(`/api/drones/${droneId}/stop`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                    }

                    alertSystem.emergency('Atterrissage d\'urgence', 'Tous les drones ont été rappelés');
                } catch (error) {
                    console.error('Erreur lors de l\'atterrissage d\'urgence:', error);
                    alertSystem.danger('Erreur', 'Impossible d\'effectuer l\'atterrissage d\'urgence');
                }
            }
        }

        // Fonction pour simuler des incidents
        async function simulateIncident(type) {
            const descriptions = {
                weather: 'Conditions météorologiques défavorables détectées',
                security: 'Intrusion détectée dans l\'espace aérien',
                technical: 'Défaillance technique du système de navigation'
            };

            try {
                const response = await fetch('/api/incidents', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: type,
                        description: descriptions[type],
                        location: {
                            lat: map.getCenter().lat,
                            lng: map.getCenter().lng
                        }
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    alertSystem.warning('Incident simulé', descriptions[type]);
                } else {
                    alertSystem.danger('Erreur', data.error);
                }
            } catch (error) {
                console.error('Erreur lors de la simulation d\'incident:', error);
                alertSystem.danger('Erreur réseau', 'Impossible de simuler l\'incident');
            }
        }

        // Fonction pour arrêter un drone spécifique
        async function stopDroneById(droneId) {
            try {
                const response = await fetch(`/api/drones/${droneId}/stop`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    alertSystem.success('Drone arrêté', `ID: ${droneId.substring(0, 8)}`);
                } else {
                    alertSystem.danger('Erreur', data.error);
                }
            } catch (error) {
                console.error('Erreur lors de l\'arrêt du drone:', error);
                alertSystem.danger('Erreur réseau', 'Impossible d\'arrêter le drone');
            }
        }

        // Fonction pour mettre à jour la position d'un drone
        function updateDronePosition(data) {
            console.log('Mise à jour position drone:', data);
            // Cette fonction sera appelée par les WebSockets pour mettre à jour les positions en temps réel
        }
    </script>
</body>
</html>
