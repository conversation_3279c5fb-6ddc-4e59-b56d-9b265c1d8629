
    <!DOCTYPE html>
    <html>
    <head>
        <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    </head>
    <body>
        <div id="map" style="height: 400px;"></div>
        <script>
            // Test basique de Leaflet
            try {
                const map = L.map('map').setView([28.7917, -7.0926], 6);
                <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);
                console.log('✅ Leaflet fonctionne');
            } catch (e) {
                console.error('❌ Erreur Leaflet:', e);
            }
            
            // Test basique de Socket.IO
            try {
                const socket = io('http://localhost:5000');
                console.log('✅ Socket.IO initialisé');
            } catch (e) {
                console.error('❌ Erreur Socket.IO:', e);
            }
        </script>
    </body>
    </html>
    