<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Carte Simple - Maroc</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        #map {
            height: 500px;
            width: 100%;
            border: 2px solid #333;
            background-color: #f0f0f0;
        }
        
        .header {
            background: linear-gradient(135deg, #c1272d 0%, #006233 50%, #c1272d 100%);
            color: white;
            padding: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background-color: #e3f2fd;
        }
        
        .city-icon {
            background-color: rgba(52, 152, 219, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            border: 2px solid white;
            white-space: nowrap;
        }
        
        .city-icon.capital {
            background-color: rgba(231, 76, 60, 0.9);
            border-color: #f1c40f;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>🇲🇦 Test Carte du Maroc - Diagnostic</h2>
    </div>
    
    <div id="status" class="status">
        <strong>État:</strong> <span id="statusText">Initialisation...</span>
    </div>
    
    <div id="map"></div>
    
    <div style="margin-top: 20px;">
        <h3>Informations de diagnostic:</h3>
        <ul id="diagnostics"></ul>
    </div>
    
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        const diagnostics = document.getElementById('diagnostics');
        const statusText = document.getElementById('statusText');
        
        function addDiagnostic(message, isError = false) {
            const li = document.createElement('li');
            li.textContent = message;
            li.style.color = isError ? 'red' : 'green';
            diagnostics.appendChild(li);
            console.log(message);
        }
        
        function updateStatus(text, isError = false) {
            statusText.textContent = text;
            statusText.style.color = isError ? 'red' : 'green';
        }
        
        // Vérifications préliminaires
        addDiagnostic('✓ Page HTML chargée');
        
        if (typeof L !== 'undefined') {
            addDiagnostic('✓ Leaflet chargé - Version: ' + L.version);
        } else {
            addDiagnostic('✗ Leaflet non chargé', true);
            updateStatus('Erreur: Leaflet non chargé', true);
        }
        
        // Configuration du Maroc
        const MOROCCO_CONFIG = {
            center: [28.7917, -7.0926],
            cities: [
                {name: "Rabat", coords: [34.0209, -6.8416], capital: true},
                {name: "Casablanca", coords: [33.5731, -7.5898]},
                {name: "Marrakech", coords: [31.6295, -7.9811]},
                {name: "Fès", coords: [34.0181, -5.0078]}
            ]
        };
        
        // Initialisation de la carte
        setTimeout(function() {
            try {
                updateStatus('Initialisation de la carte...');
                addDiagnostic('Début de l\'initialisation de la carte');
                
                // Créer la carte
                const map = L.map('map', {
                    center: MOROCCO_CONFIG.center,
                    zoom: 6,
                    minZoom: 4,
                    maxZoom: 18
                });
                
                addDiagnostic('✓ Objet carte créé');
                
                // Ajouter les tuiles
                const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors | Royaume du Maroc',
                    maxZoom: 18
                });
                
                osmLayer.addTo(map);
                addDiagnostic('✓ Couche de tuiles ajoutée');
                
                // Ajouter les villes
                MOROCCO_CONFIG.cities.forEach(city => {
                    const icon = L.divIcon({
                        className: 'city-marker',
                        html: `<div class="city-icon ${city.capital ? 'capital' : ''}">${city.name}</div>`,
                        iconSize: [city.name.length * 8 + 20, 25],
                        iconAnchor: [city.name.length * 4 + 10, 12]
                    });
                    
                    const marker = L.marker(city.coords, {icon: icon}).addTo(map);
                    marker.bindPopup(`<b>${city.name}</b>${city.capital ? '<br><i>Capitale du Royaume</i>' : ''}`);
                });
                
                addDiagnostic('✓ Marqueurs de villes ajoutés');
                
                // Ajouter une zone restreinte
                const circle = L.circle([33.3675, -7.5897], {
                    color: 'red',
                    fillColor: '#ff0000',
                    fillOpacity: 0.2,
                    radius: 5000
                }).addTo(map);
                
                circle.bindPopup('<b>Zone Restreinte</b><br>Aéroport Mohammed V');
                addDiagnostic('✓ Zone restreinte ajoutée');
                
                // Événement de chargement des tuiles
                osmLayer.on('load', function() {
                    addDiagnostic('✓ Tuiles chargées avec succès');
                    updateStatus('Carte chargée avec succès !');
                });
                
                osmLayer.on('tileerror', function(e) {
                    addDiagnostic('✗ Erreur de chargement des tuiles: ' + e.error, true);
                });
                
                // Événement de fin de chargement de la carte
                map.whenReady(function() {
                    addDiagnostic('✓ Carte prête');
                    updateStatus('Carte du Maroc opérationnelle !');
                });
                
                addDiagnostic('✓ Initialisation terminée');
                
            } catch (error) {
                addDiagnostic('✗ Erreur lors de l\'initialisation: ' + error.message, true);
                updateStatus('Erreur: ' + error.message, true);
                console.error('Erreur complète:', error);
            }
        }, 100);
    </script>
</body>
</html>
