from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO
import json
from datetime import datetime, timedelta

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'
socketio = SocketIO(app)

# Stockage des drones actifs
active_drones = {}

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/start_drone', methods=['POST'])
def start_drone():
    data = request.json
    drone_id = data.get('drone_id')
    coordinates = data.get('coordinates')
    duration = data.get('duration')
    
    if not all([drone_id, coordinates, duration]):
        return jsonify({'error': 'Missing required parameters'}), 400
    
    # Calculer la fin du vol
    end_time = datetime.now() + timedelta(minutes=int(duration))
    
    active_drones[drone_id] = {
        'coordinates': coordinates,
        'end_time': end_time.isoformat(),
        'current_position': 0
    }
    
    return jsonify({'status': 'success'})

@socketio.on('connect')
def handle_connect():
    print('Client connected')

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')

if __name__ == '__main__':
    socketio.run(app, debug=True) 