// Application de Surveillance des Drones - Royaume du Maroc
// Système avancé avec spécifications Wander-B et Thunder-B

// Variables globales
let map;
let drawnItems;
let droneMarkers = new Map();
let flightIntervals = new Map();
let timerIntervals = new Map();
let remainingTimes = new Map();
let isDrawing = false;
let polygonPoints = [];
let tempPolygon = null;
let pointMarkers = [];
let drawingTooltip = null;
let areaDisplay = null;
let dronePaths = new Map();
let currentPathIndices = new Map();
let flightPaths = new Map();

// Nouvelles variables pour les fonctionnalités avancées
let socket;
let activeDrones = new Map();
let activeIncidents = [];
let currentDroneSpecs = null;
let systemStatus = 'operational';
let restrictedZones = [];

// Configuration des coordonnées du Maroc
const MOROCCO_CONFIG = {
    center: [28.7917, -7.0926], // Centre du Maroc incluant le Sahara
    bounds: [
        [35.9224, -13.1681], // Nord-Ouest
        [20.7539, 2.1667]    // Sud-Est (incluant le Sahara marocain)
    ],
    cities: [
        {name: "Rabat", coords: [34.0209, -6.8416], capital: true, region: "Rabat-Salé-Kénitra"},
        {name: "Casablanca", coords: [33.5731, -7.5898], region: "Casablanca-Settat"},
        {name: "Marrakech", coords: [31.6295, -7.9811], region: "Marrakech-Safi"},
        {name: "Fès", coords: [34.0181, -5.0078], region: "Fès-Meknès"},
        {name: "Tanger", coords: [35.7595, -5.8340], region: "Tanger-Tétouan-Al Hoceïma"},
        {name: "Agadir", coords: [30.4278, -9.5981], region: "Souss-Massa"},
        {name: "Meknès", coords: [33.8935, -5.5473], region: "Fès-Meknès"},
        {name: "Oujda", coords: [34.6814, -1.9086], region: "Oriental"},
        {name: "Laâyoune", coords: [27.1253, -13.1625], region: "Laâyoune-Sakia El Hamra"},
        {name: "Dakhla", coords: [23.7185, -15.9582], region: "Dakhla-Oued Ed-Dahab"},
        {name: "Tétouan", coords: [35.5889, -5.3626], region: "Tanger-Tétouan-Al Hoceïma"},
        {name: "Ouarzazate", coords: [30.9335, -6.9370], region: "Drâa-Tafilalet"}
    ],
    airports: [
        {name: "Mohammed V", coords: [33.3675, -7.5897], radius: 5000, city: "Casablanca"},
        {name: "Rabat-Salé", coords: [34.0515, -6.7515], radius: 3000, city: "Rabat"},
        {name: "Marrakech Menara", coords: [31.6069, -8.0363], radius: 4000, city: "Marrakech"},
        {name: "Fès-Saïs", coords: [33.9273, -4.9779], radius: 3000, city: "Fès"},
        {name: "Agadir Al Massira", coords: [30.3250, -9.4131], radius: 4000, city: "Agadir"},
        {name: "Tanger Ibn Battouta", coords: [35.7269, -5.9169], radius: 3500, city: "Tanger"}
    ]
};

// Initialisation de la carte centrée sur le Maroc
function initMap() {
    // Créer la carte centrée sur le Maroc
    map = L.map('map', {
        center: MOROCCO_CONFIG.center,
        zoom: 6,
        minZoom: 5,
        maxZoom: 18,
        maxBounds: MOROCCO_CONFIG.bounds,
        maxBoundsViscosity: 1.0
    });
    
    // Couches de tuiles multiples pour une meilleure couverture
    const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors | Royaume du Maroc',
        maxZoom: 18
    });
    
    const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
        attribution: '© Esri, DigitalGlobe, GeoEye | Royaume du Maroc',
        maxZoom: 18
    });
    
    const terrainLayer = L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenTopoMap contributors | Royaume du Maroc',
        maxZoom: 17
    });
    
    // Ajouter la couche par défaut
    osmLayer.addTo(map);
    
    // Contrôle des couches
    const baseLayers = {
        "🗺️ Carte Standard": osmLayer,
        "🛰️ Vue Satellite": satelliteLayer,
        "🏔️ Relief": terrainLayer
    };
    
    L.control.layers(baseLayers).addTo(map);
    
    // Initialiser les groupes de couches
    drawnItems = new L.FeatureGroup().addTo(map);
    
    // Ajouter les éléments géographiques du Maroc
    addMoroccanCities();
    addRestrictedZones();
    addGeographicalFeatures();
    
    // Ajouter un contrôle d'échelle
    L.control.scale({
        metric: true,
        imperial: false,
        position: 'bottomright'
    }).addTo(map);
}

// Fonction pour ajouter les principales villes marocaines
function addMoroccanCities() {
    MOROCCO_CONFIG.cities.forEach(city => {
        const icon = L.divIcon({
            className: city.capital ? 'city-marker capital' : 'city-marker',
            html: `<div class="city-icon ${city.capital ? 'capital' : ''}">${city.name}</div>`,
            iconSize: [city.name.length * 8 + 20, 25],
            iconAnchor: [city.name.length * 4 + 10, 12]
        });
        
        const marker = L.marker(city.coords, {icon: icon}).addTo(map);
        
        const popupContent = `
            <div class="city-popup">
                <h6><strong>${city.name}</strong></h6>
                ${city.capital ? '<p class="text-danger mb-1"><i class="fas fa-crown"></i> Capitale du Royaume</p>' : ''}
                <p class="mb-1"><strong>Région:</strong> ${city.region}</p>
                <small class="text-muted">Coordonnées: ${city.coords[0].toFixed(4)}, ${city.coords[1].toFixed(4)}</small>
            </div>
        `;
        
        marker.bindPopup(popupContent);
    });
}

// Fonction pour ajouter les zones restreintes (aéroports)
function addRestrictedZones() {
    MOROCCO_CONFIG.airports.forEach(airport => {
        const circle = L.circle(airport.coords, {
            color: '#e74c3c',
            fillColor: '#e74c3c',
            fillOpacity: 0.2,
            radius: airport.radius,
            className: 'restricted-zone'
        }).addTo(map);
        
        const popupContent = `
            <div class="airport-popup">
                <h6 class="text-danger"><i class="fas fa-plane"></i> Zone Restreinte</h6>
                <p><strong>Aéroport ${airport.name}</strong></p>
                <p>Ville: ${airport.city}</p>
                <p class="text-warning mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    Vol de drones strictement interdit
                </p>
            </div>
        `;
        
        circle.bindPopup(popupContent);
        restrictedZones.push({
            type: 'airport',
            name: airport.name,
            coords: airport.coords,
            radius: airport.radius,
            layer: circle
        });
    });
}

// Fonction pour ajouter des caractéristiques géographiques
function addGeographicalFeatures() {
    // Ligne de démarcation du Sahara marocain (symbolique)
    const saharaLine = L.polyline([
        [27.6667, -13.2000],
        [27.1500, -8.6667],
        [25.0000, -8.6667]
    ], {
        color: '#f39c12',
        weight: 3,
        dashArray: '10, 10',
        opacity: 0.7
    }).addTo(map);
    
    saharaLine.bindPopup(`
        <div class="sahara-popup">
            <h6><i class="fas fa-map"></i> Sahara Marocain</h6>
            <p>Provinces du Sud du Royaume du Maroc</p>
            <small class="text-muted">Zone de surveillance étendue</small>
        </div>
    `);
    
    // Frontières symboliques
    const borders = [
        // Frontière avec l'Algérie
        [[35.1667, -2.0000], [32.0000, -2.0000], [27.6667, -8.6667]],
        // Frontière avec la Mauritanie
        [[27.1500, -8.6667], [21.3333, -17.0000]]
    ];
    
    borders.forEach((border, index) => {
        L.polyline(border, {
            color: '#95a5a6',
            weight: 2,
            dashArray: '5, 5',
            opacity: 0.5
        }).addTo(map);
    });
}

// Initialisation des WebSockets
function initWebSocket() {
    socket = io();
    
    socket.on('connect', function() {
        console.log('Connecté au serveur de surveillance');
        updateSystemStatus('operational', 'Système opérationnel');
        if (window.alertSystem) {
            alertSystem.success('Connexion', 'Connecté au système de surveillance');
        }
    });
    
    socket.on('disconnect', function() {
        console.log('Déconnecté du serveur');
        updateSystemStatus('disconnected', 'Connexion perdue');
        if (window.alertSystem) {
            alertSystem.warning('Déconnexion', 'Connexion au serveur perdue');
        }
    });
    
    socket.on('initial_state', function(data) {
        console.log('État initial reçu:', data);
        activeDrones.clear();
        data.active_drones.forEach(drone => {
            activeDrones.set(drone.id, drone);
        });
        activeIncidents = data.active_incidents || [];
        updateActiveDronesList();
        updateIncidentsList();
    });
    
    socket.on('drone_started', function(droneData) {
        console.log('Nouveau drone démarré:', droneData);
        activeDrones.set(droneData.id, droneData);
        updateActiveDronesList();
        if (window.alertSystem) {
            alertSystem.success('Drone démarré', 
                `${droneData.type} - ID: ${droneData.id.substring(0, 8)}`, 
                {playSound: true});
        }
    });
    
    socket.on('drone_position_update', function(data) {
        updateDronePosition(data);
    });
    
    socket.on('drone_stopped', function(data) {
        activeDrones.delete(data.drone_id);
        updateActiveDronesList();
        if (window.alertSystem) {
            alertSystem.info('Drone arrêté', `ID: ${data.drone_id.substring(0, 8)}`);
        }
    });
    
    socket.on('drone_completed', function(data) {
        activeDrones.delete(data.drone_id);
        updateActiveDronesList();
        if (window.alertSystem) {
            alertSystem.success('Vol terminé', `ID: ${data.drone_id.substring(0, 8)}`);
        }
    });
    
    socket.on('incident_created', function(incident) {
        activeIncidents.push(incident);
        updateIncidentsList();
        updateSystemStatus('incident', 'Incident détecté');
        if (window.alertSystem) {
            alertSystem.warning('Incident détecté', incident.description, {playSound: true});
        }
    });
    
    socket.on('emergency_landing', function(data) {
        if (window.alertSystem) {
            alertSystem.emergency('Atterrissage d\'urgence',
                `Drone ${data.drone_id.substring(0, 8)}`,
                {playSound: true});
        }
    });
}

// Fonction pour charger les spécifications d'un drone
async function loadDroneSpecs(droneType) {
    try {
        const response = await fetch(`/api/drone-specs/${droneType}`);
        const data = await response.json();

        if (response.ok) {
            currentDroneSpecs = data.specifications;
            displayDroneSpecs(data.specifications, droneType);

            // Mettre à jour la durée maximale
            const durationInput = document.getElementById('flightDuration');
            durationInput.max = data.specifications.max_flight_time;

            if (parseInt(durationInput.value) > data.specifications.max_flight_time) {
                durationInput.value = data.specifications.max_flight_time;
            }
        } else {
            console.error('Erreur lors du chargement des spécifications:', data.error);
            if (window.alertSystem) {
                alertSystem.danger('Erreur', 'Impossible de charger les spécifications du drone');
            }
        }
    } catch (error) {
        console.error('Erreur réseau:', error);
        if (window.alertSystem) {
            alertSystem.danger('Erreur réseau', 'Impossible de contacter le serveur');
        }
    }
}

// Fonction pour afficher les spécifications du drone
function displayDroneSpecs(specs, droneType) {
    const specsContent = document.getElementById('specsContent');
    const typeIcon = droneType === 'Wander-B' ? 'fas fa-eye' : 'fas fa-shield-alt';
    const typeColor = droneType === 'Wander-B' ? '#3498db' : '#e74c3c';

    specsContent.innerHTML = `
        <div class="spec-header mb-2">
            <i class="${typeIcon}" style="color: ${typeColor};"></i>
            <strong>${droneType}</strong>
        </div>
        <div class="spec-item">
            <span class="spec-label"><i class="fas fa-tachometer-alt"></i> Vitesse max:</span>
            <span class="spec-value">${specs.max_speed} km/h</span>
        </div>
        <div class="spec-item">
            <span class="spec-label"><i class="fas fa-mountain"></i> Altitude max:</span>
            <span class="spec-value">${specs.max_altitude} m</span>
        </div>
        <div class="spec-item">
            <span class="spec-label"><i class="fas fa-clock"></i> Autonomie:</span>
            <span class="spec-value">${specs.max_flight_time} min</span>
        </div>
        <div class="spec-item">
            <span class="spec-label"><i class="fas fa-broadcast-tower"></i> Portée:</span>
            <span class="spec-value">${specs.range} km</span>
        </div>
        <div class="spec-item">
            <span class="spec-label"><i class="fas fa-weight-hanging"></i> Charge utile:</span>
            <span class="spec-value">${specs.payload_capacity} kg</span>
        </div>
        <div class="spec-item">
            <span class="spec-label"><i class="fas fa-camera"></i> Caméra:</span>
            <span class="spec-value">${specs.camera_resolution}</span>
        </div>
        <div class="mt-3">
            <small class="text-muted"><i class="fas fa-star"></i> Fonctionnalités:</small><br>
            ${specs.special_features.map(feature =>
                `<small class="badge bg-secondary me-1 mb-1">${feature}</small>`
            ).join('')}
        </div>
    `;
}

// Fonction pour mettre à jour le statut du système
function updateSystemStatus(status, message) {
    const statusElement = document.getElementById('systemStatus');
    systemStatus = status;

    statusElement.className = 'alert';
    statusElement.innerHTML = '';

    switch (status) {
        case 'operational':
            statusElement.classList.add('alert-success');
            statusElement.innerHTML = '<i class="fas fa-check-circle"></i> ' + message;
            break;
        case 'incident':
            statusElement.classList.add('alert-warning');
            statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i> ' + message;
            break;
        case 'emergency':
            statusElement.classList.add('alert-danger');
            statusElement.innerHTML = '<i class="fas fa-exclamation-circle"></i> ' + message;
            break;
        case 'disconnected':
            statusElement.classList.add('alert-danger');
            statusElement.innerHTML = '<i class="fas fa-wifi"></i> ' + message;
            break;
    }
}

// Fonction pour mettre à jour la liste des drones actifs
function updateActiveDronesList() {
    const dronesList = document.getElementById('activeDronesList');

    if (activeDrones.size === 0) {
        dronesList.innerHTML = `
            <div class="list-group-item text-muted text-center">
                <i class="fas fa-info-circle me-1"></i>
                Aucun drone en vol
            </div>
        `;
        return;
    }

    let html = '';
    activeDrones.forEach((drone, droneId) => {
        const shortId = droneId.substring(0, 8);
        const droneClass = drone.type.toLowerCase().replace('-', '-');
        const statusClass = `status-${drone.status}`;
        const typeIcon = drone.type === 'Wander-B' ? 'fas fa-eye' : 'fas fa-shield-alt';
        const typeColor = drone.type === 'Wander-B' ? '#3498db' : '#e74c3c';

        html += `
            <div class="list-group-item drone-item ${droneClass}">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">
                            <i class="${typeIcon}" style="color: ${typeColor};"></i>
                            ${drone.type}
                        </h6>
                        <small class="text-muted">ID: ${shortId}</small>
                    </div>
                    <div class="text-end">
                        <span class="drone-status ${statusClass}">${drone.status}</span>
                        <br>
                        <button class="btn btn-sm btn-outline-danger mt-1" onclick="stopDroneById('${droneId}')">
                            <i class="fas fa-stop"></i> Arrêter
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    dronesList.innerHTML = html;
}

// Fonction pour mettre à jour la liste des incidents
function updateIncidentsList() {
    const incidentsContainer = document.getElementById('activeIncidents');
    const incidentsList = document.getElementById('incidentsList');

    if (activeIncidents.length === 0) {
        incidentsContainer.style.display = 'none';
        return;
    }

    incidentsContainer.style.display = 'block';
    let html = '';

    activeIncidents.forEach(incident => {
        const timestamp = new Date(incident.timestamp).toLocaleTimeString();
        const typeIcon = getIncidentIcon(incident.type);

        html += `
            <div class="alert alert-warning alert-sm mb-2">
                <i class="${typeIcon} me-1"></i>
                <strong>${incident.type}:</strong> ${incident.description}
                <br><small class="text-muted">${timestamp}</small>
            </div>
        `;
    });

    incidentsList.innerHTML = html;
}

// Fonction pour obtenir l'icône d'un incident
function getIncidentIcon(type) {
    const icons = {
        weather: 'fas fa-cloud-rain',
        security: 'fas fa-shield-alt',
        technical: 'fas fa-wrench',
        airspace: 'fas fa-plane'
    };
    return icons[type] || 'fas fa-exclamation-triangle';
}
