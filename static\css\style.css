body {
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow: hidden;
}

.container-fluid {
    height: 100vh;
    padding: 0;
}

.row {
    height: 100%;
    margin: 0;
}

.sidebar {
    background-color: #f8f9fa;
    padding: 20px;
    height: 100vh;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

#map {
    height: 100vh;
    width: 100%;
}

.btn-check:checked + .btn-outline-primary {
    background-color: #0d6efd;
    color: white;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5c636a;
    border-color: #565e64;
}

/* Style pour les instructions */
.list-unstyled li {
    margin-bottom: 8px;
    color: #6c757d;
}

/* Style pour le titre */
h2 {
    color: #212529;
    font-weight: 600;
}

/* Style pour les labels */
.form-label {
    font-weight: 500;
    color: #495057;
}

/* Style pour le minuteur */
.timer-display {
    font-size: 1.5rem;
    font-weight: bold;
    color: #0d6efd;
    text-align: center;
    padding: 10px;
    background-color: #e9ecef;
    border-radius: 5px;
    margin-top: 10px;
}

/* Animation pour le drone */
@keyframes droneAnimation {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(0);
    }
}

.drone-icon {
    animation: droneAnimation 2s infinite;
}

.drone-icon.wander-b img {
    filter: hue-rotate(200deg);
}

.drone-icon.thunder-b img {
    filter: hue-rotate(320deg);
}

.drone-marker {
    position: relative;
    animation: pulse 1.5s infinite;
}

.drone-marker::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: inherit;
    opacity: 0.3;
    animation: ripple 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes ripple {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0.3;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

/* Style pour le chemin du drone */
.drone-path {
    stroke-dasharray: 5, 5;
    animation: dash 1s linear infinite;
}

@keyframes dash {
    to {
        stroke-dashoffset: -10;
    }
} 