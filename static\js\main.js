// Initialisation de la carte
const map = L.map('map').setView([28.5, -10.0], 6); // Centré sur le Maroc

// Ajout du fond de carte
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors'
}).addTo(map);

// Variables globales
let drawingPolygon = false;
let currentPolygon = null;
let polygonPoints = [];
let droneMarker = null;
let dronePath = null;
let socket = io();
let pointMarkers = [];
let flightTimer = null;
let remainingTime = 0;

// Icônes personnalisées pour les drones
const droneIcons = {
    'Wander-B': L.icon({
        iconUrl: 'static/css/dd.png',
        iconSize: [40, 40],
        iconAnchor: [20, 20],
        className: 'drone-icon wander-b'
    }),
    'Thunder-B': L.icon({
        iconUrl: 'static/css/dd.png',
        iconSize: [40, 40],
        iconAnchor: [20, 20],
        className: 'drone-icon thunder-b'
    })
};

// Fonction pour formater le temps
function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// Fonction pour mettre à jour le minuteur
function updateTimer() {
    const timerElement = document.getElementById('flightTimer');
    if (timerElement) {
        timerElement.textContent = `Temps restant: ${formatTime(remainingTime)}`;
    }
}

// Gestionnaire d'événements pour le clic sur la carte
map.on('click', function(e) {
    if (drawingPolygon && polygonPoints.length < 4) {
        const point = e.latlng;
        polygonPoints.push(point);
        
        // Ajouter un marqueur pour le point
        const marker = L.marker(point).addTo(map);
        pointMarkers.push(marker);
        
        // Mettre à jour le polygone
        if (currentPolygon) {
            map.removeLayer(currentPolygon);
        }
        
        if (polygonPoints.length > 1) {
            currentPolygon = L.polygon(polygonPoints, {
                color: 'blue',
                fillColor: '#3388ff',
                fillOpacity: 0.2
            }).addTo(map);
        }
        
        // Si nous avons 4 points, fermer automatiquement le polygone
        if (polygonPoints.length === 4) {
            drawingPolygon = false;
            polygonPoints.push(polygonPoints[0]); // Fermer le polygone
            
            if (currentPolygon) {
                map.removeLayer(currentPolygon);
            }
            
            currentPolygon = L.polygon(polygonPoints, {
                color: 'blue',
                fillColor: '#3388ff',
                fillOpacity: 0.2
            }).addTo(map);
        }
    }
});

// Gestionnaire d'événements pour le clic droit
map.on('contextmenu', function(e) {
    if (drawingPolygon && polygonPoints.length >= 3) {
        drawingPolygon = false;
        // Fermer le polygone en ajoutant le premier point à la fin
        polygonPoints.push(polygonPoints[0]);
        
        if (currentPolygon) {
            map.removeLayer(currentPolygon);
        }
        
        currentPolygon = L.polygon(polygonPoints, {
            color: 'blue',
            fillColor: '#3388ff',
            fillOpacity: 0.2
        }).addTo(map);
    }
});

// Gestionnaire pour le bouton "Dessiner un polygone"
document.getElementById('startDrawing').addEventListener('click', function() {
    // Nettoyer les éléments existants
    if (currentPolygon) {
        map.removeLayer(currentPolygon);
        currentPolygon = null;
    }
    pointMarkers.forEach(marker => map.removeLayer(marker));
    pointMarkers = [];
    polygonPoints = [];
    
    drawingPolygon = true;
    this.disabled = true;
    document.getElementById('startFlight').disabled = false;
});

// Gestionnaire pour le bouton "Démarrer le vol"
document.getElementById('startFlight').addEventListener('click', function() {
    const droneType = document.querySelector('input[name="droneType"]:checked').value;
    const duration = parseInt(document.getElementById('flightDuration').value);
    
    if (polygonPoints.length < 3) {
        alert('Veuillez d\'abord dessiner un polygone valide');
        return;
    }
    
    // Désactiver les boutons pendant le vol
    this.disabled = true;
    document.getElementById('startDrawing').disabled = true;
    document.getElementById('clearMap').disabled = true;
    
    // Initialiser le minuteur
    remainingTime = duration * 60;
    updateTimer();
    
    // Envoyer les données au serveur
    fetch('/start_drone', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            drone_id: Date.now().toString(),
            coordinates: polygonPoints.map(point => [point.lat, point.lng]),
            duration: duration,
            type: droneType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            startDroneAnimation(polygonPoints, droneType, duration);
        }
    })
    .catch(error => console.error('Error:', error));
});

// Fonction pour animer le drone
function startDroneAnimation(coordinates, droneType, duration) {
    if (droneMarker) {
        map.removeLayer(droneMarker);
    }
    if (dronePath) {
        map.removeLayer(dronePath);
    }
    
    // Créer le marqueur du drone
    const icon = droneIcons[droneType];
    droneMarker = L.marker(coordinates[0], {
        icon: icon,
        zIndexOffset: 1000,
        riseOnHover: true
    }).addTo(map);
    
    // Créer le chemin du drone
    dronePath = L.polyline(coordinates, {
        color: droneType === 'Wander-B' ? '#0d6efd' : '#dc3545',
        weight: 3,
        opacity: 0.7,
        dashArray: '5, 5',
        className: 'drone-path'
    }).addTo(map);
    
    let currentIndex = 0;
    const totalPoints = coordinates.length;
    const interval = (duration * 60 * 1000) / totalPoints;
    
    // Démarrer le minuteur
    flightTimer = setInterval(() => {
        remainingTime--;
        updateTimer();
        
        if (remainingTime <= 0) {
            clearInterval(flightTimer);
            flightTimer = null;
        }
    }, 1000);
    
    const animation = setInterval(() => {
        if (currentIndex < totalPoints - 1) {
            currentIndex++;
            droneMarker.setLatLng(coordinates[currentIndex]);
        } else {
            clearInterval(animation);
            clearInterval(flightTimer);
            map.removeLayer(droneMarker);
            map.removeLayer(dronePath);
            
            // Réactiver les boutons
            document.getElementById('startFlight').disabled = false;
            document.getElementById('startDrawing').disabled = false;
            document.getElementById('clearMap').disabled = false;
            
            // Réinitialiser le minuteur
            const timerElement = document.getElementById('flightTimer');
            if (timerElement) {
                timerElement.textContent = '';
            }
        }
    }, interval);
}

// Gestionnaire pour le bouton "Effacer la carte"
document.getElementById('clearMap').addEventListener('click', function() {
    if (currentPolygon) {
        map.removeLayer(currentPolygon);
        currentPolygon = null;
    }
    if (droneMarker) {
        map.removeLayer(droneMarker);
        droneMarker = null;
    }
    if (dronePath) {
        map.removeLayer(dronePath);
        dronePath = null;
    }
    pointMarkers.forEach(marker => map.removeLayer(marker));
    pointMarkers = [];
    polygonPoints = [];
    drawingPolygon = false;
    
    // Réinitialiser le minuteur
    if (flightTimer) {
        clearInterval(flightTimer);
        flightTimer = null;
    }
    const timerElement = document.getElementById('flightTimer');
    if (timerElement) {
        timerElement.textContent = '';
    }
    
    document.getElementById('startDrawing').disabled = false;
    document.getElementById('startFlight').disabled = true;
}); 