#!/usr/bin/env python3
"""
Test pour vérifier que l'application frontend fonctionne correctement
"""

import requests
import time

def test_frontend_resources():
    """Tester que toutes les ressources frontend sont accessibles"""
    base_url = "http://localhost:5000"
    
    print("🌐 Test des ressources frontend")
    print("=" * 40)
    
    # Test de la page principale
    try:
        response = requests.get(base_url)
        if response.status_code == 200:
            print("✅ Page principale accessible")
            
            # Vérifier la présence de Leaflet
            if "leaflet" in response.text.lower():
                print("✅ Leaflet détecté dans le HTML")
            else:
                print("❌ Leaflet non détecté")
            
            # Vérifier la présence de la carte
            if 'id="map"' in response.text:
                print("✅ Élément carte présent")
            else:
                print("❌ Élément carte manquant")
            
            # Vérifier les scripts
            if "drone-system.js" in response.text:
                print("✅ Script drone-system.js inclus")
            else:
                print("❌ Script drone-system.js manquant")
                
            if "alerts.js" in response.text:
                print("✅ Script alerts.js inclus")
            else:
                print("❌ Script alerts.js manquant")
                
        else:
            print(f"❌ Page principale inaccessible - Status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur lors du test de la page: {e}")
    
    # Test des ressources statiques
    static_resources = [
        "/static/js/alerts.js",
        "/static/js/drone-system.js",
        "/static/css/style.css"
    ]
    
    print("\n📁 Test des ressources statiques")
    print("=" * 40)
    
    for resource in static_resources:
        try:
            response = requests.get(f"{base_url}{resource}")
            if response.status_code == 200:
                print(f"✅ {resource}")
            else:
                print(f"❌ {resource} - Status: {response.status_code}")
        except Exception as e:
            print(f"❌ {resource} - Erreur: {e}")
    
    # Test des API endpoints
    print("\n🔌 Test des endpoints API")
    print("=" * 40)
    
    api_endpoints = [
        "/api/drone-specs/Wander-B",
        "/api/drone-specs/Thunder-B",
        "/api/drones/active",
        "/api/incidents/active"
    ]
    
    for endpoint in api_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}")
            if response.status_code == 200:
                print(f"✅ {endpoint}")
            else:
                print(f"❌ {endpoint} - Status: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} - Erreur: {e}")

def test_javascript_functionality():
    """Tester les fonctionnalités JavaScript de base"""
    print("\n🔧 Test des fonctionnalités JavaScript")
    print("=" * 40)
    
    # Créer un test HTML simple pour vérifier les fonctions
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    </head>
    <body>
        <div id="map" style="height: 400px;"></div>
        <script>
            // Test basique de Leaflet
            try {
                const map = L.map('map').setView([28.7917, -7.0926], 6);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);
                console.log('✅ Leaflet fonctionne');
            } catch (e) {
                console.error('❌ Erreur Leaflet:', e);
            }
            
            // Test basique de Socket.IO
            try {
                const socket = io('http://localhost:5000');
                console.log('✅ Socket.IO initialisé');
            } catch (e) {
                console.error('❌ Erreur Socket.IO:', e);
            }
        </script>
    </body>
    </html>
    """
    
    # Sauvegarder le test
    with open('test_js.html', 'w', encoding='utf-8') as f:
        f.write(test_html)
    
    print("✅ Fichier de test JavaScript créé: test_js.html")
    print("   Ouvrez ce fichier dans un navigateur et vérifiez la console")

if __name__ == "__main__":
    print("🇲🇦 TEST FRONTEND - APPLICATION DE SURVEILLANCE DES DRONES")
    print("=" * 60)
    
    test_frontend_resources()
    test_javascript_functionality()
    
    print("\n" + "=" * 60)
    print("🎯 TESTS FRONTEND TERMINÉS")
    print("=" * 60)
    print("\n💡 Pour tester l'interface complète:")
    print("   1. Ouvrez http://localhost:5000 dans votre navigateur")
    print("   2. Ouvrez les outils de développement (F12)")
    print("   3. Vérifiez la console pour les erreurs JavaScript")
    print("   4. Testez les fonctionnalités de dessin et de vol")
