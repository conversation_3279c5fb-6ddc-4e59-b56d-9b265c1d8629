<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Carte Maroc</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        #map {
            height: 500px;
            width: 100%;
        }
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }
        .city-icon {
            background-color: rgba(52, 152, 219, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            border: 2px solid white;
            white-space: nowrap;
        }
        .city-icon.capital {
            background-color: rgba(231, 76, 60, 0.9);
            border-color: #f1c40f;
        }
    </style>
</head>
<body>
    <h1>🇲🇦 Test de la Carte du Maroc</h1>
    <div id="map"></div>
    
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Configuration du Maroc
        const MOROCCO_CONFIG = {
            center: [28.7917, -7.0926],
            cities: [
                {name: "Rabat", coords: [34.0209, -6.8416], capital: true},
                {name: "Casablanca", coords: [33.5731, -7.5898]},
                {name: "Marrakech", coords: [31.6295, -7.9811]},
                {name: "Fès", coords: [34.0181, -5.0078]},
                {name: "Tanger", coords: [35.7595, -5.8340]},
                {name: "Agadir", coords: [30.4278, -9.5981]},
                {name: "Laâyoune", coords: [27.1253, -13.1625]},
                {name: "Dakhla", coords: [23.7185, -15.9582]}
            ]
        };
        
        // Initialiser la carte
        const map = L.map('map').setView(MOROCCO_CONFIG.center, 6);
        
        // Ajouter les tuiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors | Royaume du Maroc',
            maxZoom: 18
        }).addTo(map);
        
        // Ajouter les villes
        MOROCCO_CONFIG.cities.forEach(city => {
            const icon = L.divIcon({
                className: 'city-marker',
                html: `<div class="city-icon ${city.capital ? 'capital' : ''}">${city.name}</div>`,
                iconSize: [city.name.length * 8 + 20, 25],
                iconAnchor: [city.name.length * 4 + 10, 12]
            });
            
            L.marker(city.coords, {icon: icon})
                .addTo(map)
                .bindPopup(`<b>${city.name}</b>${city.capital ? '<br><i>Capitale du Royaume</i>' : ''}`);
        });
        
        // Ajouter une zone restreinte (exemple)
        L.circle([33.3675, -7.5897], {
            color: 'red',
            fillColor: '#ff0000',
            fillOpacity: 0.2,
            radius: 5000
        }).addTo(map).bindPopup('<b>Zone Restreinte</b><br>Aéroport Mohammed V');
        
        console.log('Carte du Maroc initialisée avec succès !');
    </script>
</body>
</html>
