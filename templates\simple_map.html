<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carte Simple - Maroc</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        .morocco-header {
            background: linear-gradient(135deg, #c1272d 0%, #006233 50%, #c1272d 100%);
            color: white;
            padding: 15px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            position: relative;
            z-index: 1000;
        }
        
        #map {
            height: calc(100vh - 80px);
            width: 100%;
            z-index: 10 !important;
            position: relative !important;
            background-color: #e8f4fd;
        }
        
        .leaflet-container {
            z-index: 10 !important;
            position: relative !important;
        }
        
        .city-icon {
            background-color: rgba(52, 152, 219, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            border: 2px solid white;
            white-space: nowrap;
        }
        
        .city-icon.capital {
            background-color: rgba(231, 76, 60, 0.9);
            border-color: #f1c40f;
        }
        
        .city-marker {
            background: transparent !important;
            border: none !important;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <!-- Header avec identité marocaine -->
    <div class="morocco-header">
        <h4 class="mb-0">
            <span>🇲🇦</span>
            Système de Surveillance des Drones - Royaume du Maroc
            <small class="ms-3">المملكة المغربية - نظام مراقبة الطائرات بدون طيار</small>
        </h4>
    </div>

    <!-- Carte -->
    <div id="map">
        <div id="loading" class="loading-overlay">
            <div class="text-center">
                <i class="fas fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                <h5>Chargement de la carte du Maroc...</h5>
                <p class="text-muted">Initialisation en cours</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        console.log('🇲🇦 Démarrage de l\'application de surveillance des drones du Maroc');
        
        // Configuration du Maroc
        const MOROCCO_CONFIG = {
            center: [28.7917, -7.0926],
            cities: [
                {name: "Rabat", coords: [34.0209, -6.8416], capital: true, region: "Rabat-Salé-Kénitra"},
                {name: "Casablanca", coords: [33.5731, -7.5898], region: "Casablanca-Settat"},
                {name: "Marrakech", coords: [31.6295, -7.9811], region: "Marrakech-Safi"},
                {name: "Fès", coords: [34.0181, -5.0078], region: "Fès-Meknès"},
                {name: "Tanger", coords: [35.7595, -5.8340], region: "Tanger-Tétouan-Al Hoceïma"},
                {name: "Agadir", coords: [30.4278, -9.5981], region: "Souss-Massa"},
                {name: "Laâyoune", coords: [27.1253, -13.1625], region: "Laâyoune-Sakia El Hamra"},
                {name: "Dakhla", coords: [23.7185, -15.9582], region: "Dakhla-Oued Ed-Dahab"}
            ],
            airports: [
                {name: "Mohammed V", coords: [33.3675, -7.5897], radius: 5000, city: "Casablanca"},
                {name: "Rabat-Salé", coords: [34.0515, -6.7515], radius: 3000, city: "Rabat"},
                {name: "Marrakech Menara", coords: [31.6069, -8.0363], radius: 4000, city: "Marrakech"},
                {name: "Fès-Saïs", coords: [33.9273, -4.9779], radius: 3000, city: "Fès"}
            ]
        };
        
        function initMap() {
            console.log('🗺️ Initialisation de la carte du Maroc...');
            
            try {
                // Vérifier Leaflet
                if (typeof L === 'undefined') {
                    throw new Error('Leaflet non chargé');
                }
                console.log('✅ Leaflet chargé - Version:', L.version);
                
                // Créer la carte
                const map = L.map('map', {
                    center: MOROCCO_CONFIG.center,
                    zoom: 6,
                    minZoom: 4,
                    maxZoom: 18,
                    zoomControl: true,
                    attributionControl: true
                });
                
                console.log('✅ Carte créée');
                
                // Ajouter les tuiles
                const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors | Royaume du Maroc',
                    maxZoom: 18
                });
                
                osmLayer.addTo(map);
                console.log('✅ Tuiles ajoutées');
                
                // Ajouter les villes
                MOROCCO_CONFIG.cities.forEach(city => {
                    const icon = L.divIcon({
                        className: 'city-marker',
                        html: `<div class="city-icon ${city.capital ? 'capital' : ''}">${city.name}</div>`,
                        iconSize: [city.name.length * 8 + 20, 25],
                        iconAnchor: [city.name.length * 4 + 10, 12]
                    });
                    
                    const marker = L.marker(city.coords, {icon: icon}).addTo(map);
                    marker.bindPopup(`
                        <div>
                            <h6><strong>${city.name}</strong></h6>
                            ${city.capital ? '<p class="text-danger mb-1"><i class="fas fa-crown"></i> Capitale du Royaume</p>' : ''}
                            <p class="mb-1"><strong>Région:</strong> ${city.region}</p>
                            <small class="text-muted">Coordonnées: ${city.coords[0].toFixed(4)}, ${city.coords[1].toFixed(4)}</small>
                        </div>
                    `);
                });
                
                console.log('✅ Villes ajoutées');
                
                // Ajouter les zones restreintes
                MOROCCO_CONFIG.airports.forEach(airport => {
                    const circle = L.circle(airport.coords, {
                        color: '#e74c3c',
                        fillColor: '#e74c3c',
                        fillOpacity: 0.2,
                        radius: airport.radius
                    }).addTo(map);
                    
                    circle.bindPopup(`
                        <div>
                            <h6 class="text-danger"><i class="fas fa-plane"></i> Zone Restreinte</h6>
                            <p><strong>Aéroport ${airport.name}</strong></p>
                            <p>Ville: ${airport.city}</p>
                            <p class="text-warning mb-0">
                                <i class="fas fa-exclamation-triangle"></i>
                                Vol de drones strictement interdit
                            </p>
                        </div>
                    `);
                });
                
                console.log('✅ Zones restreintes ajoutées');
                
                // Événements
                map.whenReady(function() {
                    console.log('✅ Carte prête');
                    
                    // Supprimer l'overlay de chargement
                    const loading = document.getElementById('loading');
                    if (loading) {
                        loading.style.display = 'none';
                    }
                    
                    // Forcer le redimensionnement
                    setTimeout(() => {
                        map.invalidateSize();
                        console.log('✅ Taille de carte invalidée');
                    }, 100);
                });
                
                osmLayer.on('load', function() {
                    console.log('✅ Tuiles chargées');
                });
                
                console.log('🎉 Carte du Maroc initialisée avec succès !');
                
            } catch (error) {
                console.error('❌ Erreur lors de l\'initialisation:', error);
                
                // Afficher l'erreur
                const loading = document.getElementById('loading');
                if (loading) {
                    loading.innerHTML = `
                        <div class="text-center">
                            <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                            <h5>Erreur de chargement</h5>
                            <p class="text-muted">${error.message}</p>
                            <button class="btn btn-primary" onclick="location.reload()">
                                <i class="fas fa-refresh me-1"></i> Rafraîchir
                            </button>
                        </div>
                    `;
                }
            }
        }
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM chargé');
            
            // Attendre que Leaflet soit complètement chargé
            setTimeout(initMap, 500);
        });
    </script>
</body>
</html>
