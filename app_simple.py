from flask import Flask, render_template, jsonify, request
from flask_socketio import <PERSON><PERSON><PERSON>, emit
import json
import uuid
import secrets
from datetime import datetime, timedelta
from enum import Enum

# Configuration de l'application
app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(32)
socketio = SocketIO(app, cors_allowed_origins="*")

# Énumérations pour les types de drones et statuts
class DroneType(Enum):
    WANDER_B = "Wander-B"
    THUNDER_B = "Thunder-B"

class DroneStatus(Enum):
    IDLE = "idle"
    FLYING = "flying"
    EMERGENCY = "emergency"
    MAINTENANCE = "maintenance"

class IncidentType(Enum):
    WEATHER = "weather"
    TECHNICAL = "technical"
    SECURITY = "security"
    AIRSPACE = "airspace"

# Spécifications des drones (version simplifiée)
DRONE_SPECIFICATIONS = {
    "Wander-B": {
        'max_speed': 45.0,
        'max_altitude': 500.0,
        'max_flight_time': 90,
        'payload_capacity': 2.5,
        'range': 15.0,
        'camera_resolution': "4K Ultra HD",
        'special_features': [
            "Vision nocturne avancée",
            "Mode furtif",
            "Reconnaissance légère",
            "Détection thermique",
            "Navigation GPS précise",
            "Résistance au vent 40 km/h"
        ]
    },
    "Thunder-B": {
        'max_speed': 80.0,
        'max_altitude': 1000.0,
        'max_flight_time': 180,
        'payload_capacity': 8.0,
        'range': 50.0,
        'camera_resolution': "8K Professional",
        'special_features': [
            "Radar avancé longue portée",
            "Surveillance lourde",
            "Résistance météo extrême",
            "Communication longue portée",
            "Système de défense intégré",
            "Capacité de charge lourde",
            "Mode autonome avancé"
        ]
    }
}

# Stockage des données en mémoire
active_drones = {}
incidents = []
flight_history = []

def check_active_incidents():
    """Vérifier s'il y a des incidents actifs qui empêchent le vol"""
    current_time = datetime.now()
    for incident in incidents:
        if incident.get('active', False):
            incident_time = datetime.fromisoformat(incident['timestamp'])
            # Les incidents restent actifs pendant 30 minutes
            if (current_time - incident_time).total_seconds() < 1800:
                return True
    return False

def get_active_incidents():
    """Récupérer la liste des incidents actifs"""
    current_time = datetime.now()
    active = []
    for incident in incidents:
        if incident.get('active', False):
            incident_time = datetime.fromisoformat(incident['timestamp'])
            if (current_time - incident_time).total_seconds() < 1800:
                active.append(incident)
    return active

# Routes principales
@app.route('/')
def index():
    """Page principale de l'application"""
    return render_template('index.html')

@app.route('/debug')
def debug():
    """Page de debug pour tester la carte"""
    return render_template('debug.html')

@app.route('/api/drone-specs/<drone_type>')
def get_drone_specs(drone_type):
    """Récupérer les spécifications d'un type de drone"""
    if drone_type in DRONE_SPECIFICATIONS:
        specs = DRONE_SPECIFICATIONS[drone_type]
        return jsonify({
            'type': drone_type,
            'specifications': specs
        })
    else:
        return jsonify({'error': 'Type de drone invalide'}), 400

@app.route('/api/start_drone', methods=['POST'])
def start_drone():
    """Démarrer un vol de drone"""
    data = request.json
    drone_type = data.get('drone_type')
    coordinates = data.get('coordinates')
    duration = data.get('duration')
    
    if not all([drone_type, coordinates, duration]):
        return jsonify({'error': 'Paramètres manquants'}), 400
    
    # Vérifier le type de drone
    if drone_type not in DRONE_SPECIFICATIONS:
        return jsonify({'error': 'Type de drone invalide'}), 400
    
    specs = DRONE_SPECIFICATIONS[drone_type]
    
    # Vérifier la durée maximale
    if int(duration) > specs['max_flight_time']:
        return jsonify({
            'error': f'Durée de vol trop longue. Maximum pour {drone_type}: {specs["max_flight_time"]} minutes'
        }), 400
    
    # Vérifier les incidents actifs
    if check_active_incidents():
        return jsonify({
            'error': 'Vol interdit: incident de sécurité détecté',
            'incident_details': get_active_incidents()
        }), 403
    
    # Générer un ID unique pour le drone
    drone_id = str(uuid.uuid4())
    
    # Calculer la fin du vol
    start_time = datetime.now()
    end_time = start_time + timedelta(minutes=int(duration))
    
    # Créer l'enregistrement du drone
    drone_data = {
        'id': drone_id,
        'type': drone_type,
        'coordinates': coordinates,
        'duration': int(duration),
        'start_time': start_time.isoformat(),
        'end_time': end_time.isoformat(),
        'status': DroneStatus.FLYING.value,
        'current_position': 0,
        'specifications': {
            'max_speed': specs['max_speed'],
            'max_altitude': specs['max_altitude'],
            'range': specs['range']
        }
    }
    
    active_drones[drone_id] = drone_data
    
    # Notifier tous les clients connectés
    socketio.emit('drone_started', drone_data)
    
    return jsonify({
        'status': 'success',
        'drone_id': drone_id,
        'drone_data': drone_data
    })

@app.route('/api/drones/active', methods=['GET'])
def get_active_drones():
    """Récupérer tous les drones actifs"""
    return jsonify(list(active_drones.values()))

@app.route('/api/drones/<drone_id>/stop', methods=['POST'])
def stop_drone(drone_id):
    """Arrêter un drone spécifique"""
    if drone_id not in active_drones:
        return jsonify({'error': 'Drone non trouvé'}), 404
    
    drone_data = active_drones[drone_id]
    drone_data['status'] = DroneStatus.IDLE.value
    drone_data['end_time'] = datetime.now().isoformat()
    
    # Ajouter à l'historique
    flight_history.append({
        'drone_id': drone_id,
        'type': drone_data['type'],
        'end_time': datetime.now().isoformat(),
        'status': 'stopped_manually'
    })
    
    # Supprimer des drones actifs
    del active_drones[drone_id]
    
    # Notifier les clients
    socketio.emit('drone_stopped', {'drone_id': drone_id})
    
    return jsonify({'status': 'success'})

@app.route('/api/incidents', methods=['POST'])
def create_incident():
    """Créer un nouvel incident"""
    data = request.json
    incident_type_str = data.get('type')
    description = data.get('description')
    location = data.get('location')
    
    if not all([incident_type_str, description]):
        return jsonify({'error': 'Type et description requis'}), 400
    
    try:
        incident_type = IncidentType(incident_type_str)
        incident = {
            'id': str(uuid.uuid4()),
            'type': incident_type.value,
            'description': description,
            'timestamp': datetime.now().isoformat(),
            'location': location,
            'active': True
        }
        incidents.append(incident)
        
        # Notifier tous les clients
        socketio.emit('incident_created', incident)
        
        return jsonify(incident)
    except ValueError:
        return jsonify({'error': 'Type d\'incident invalide'}), 400

@app.route('/api/incidents/active', methods=['GET'])
def get_active_incidents_api():
    """Récupérer les incidents actifs"""
    return jsonify(get_active_incidents())

@socketio.on('connect')
def handle_connect():
    print('Client connecté')
    # Envoyer l'état actuel au nouveau client
    emit('initial_state', {
        'active_drones': list(active_drones.values()),
        'active_incidents': get_active_incidents(),
        'restricted_zones': []
    })

@socketio.on('disconnect')
def handle_disconnect():
    print('Client déconnecté')

if __name__ == '__main__':
    print("Démarrage de l'application de surveillance des drones...")
    print("Application disponible sur http://localhost:5000")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
